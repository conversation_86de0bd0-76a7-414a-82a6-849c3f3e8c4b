# 匿名社交网络系统

一个基于 K-匿名化的社交网络系统，支持用户发布动态、评论、点赞等社交功能，同时保护用户隐私。

## 系统功能

### 用户功能
- 用户注册与登录
  - 支持用户名、昵称、密码注册
  - 登录状态持久化
  - 表单验证
- 个人资料管理
  - 修改昵称
  - 上传头像
  - 修改密码
- 社交功能
  - 发布动态（支持文字和图片）
  - 评论动态
  - 点赞动态
  - 关注其他用户
  - 查看关注列表

### 管理功能
- 仪表盘数据统计
  - 总体数据统计（用户数、动态数、评论数、点赞数）
  - 今日数据统计
  - 7日数据趋势分析（折线图展示）
- K-匿名化配置管理
  - 创建、编辑、启用/禁用配置
  - 支持多种匿名化策略（基础、增强、严格）
  - K值动态配置

### 隐私保护
- K-匿名化实现
  - 用户信息动态脱敏
  - 多级匿名化策略
  - 基于AOP的非侵入式实现
- 数据安全
  - Token认证
  - 密码加密存储
  - 权限控制

## 技术架构

### 后端技术栈
- 核心框架：Spring Boot 2.7.x
- 数据库：MySQL 8.0
- ORM框架：MyBatis-Plus
- 安全框架：Spring Security + JWT
- 其他：
  - Lombok：简化开发
  - Validation：参数校验
  - AOP：切面编程

### 前端技术栈
- 核心框架：Vue 3
- 状态管理：Pinia
- UI组件：Element Plus
- 路由：Vue Router
- 图表：ECharts
- 工具库：
  - Axios：HTTP请求
  - dayjs：日期处理

## 重点代码

### K-匿名化实现
```java
// K-匿名化切面
@Aspect
@Component
public class KAnonymityAspect {
    // 处理用户信息
    @AfterReturning(pointcut = "execution(...)", returning = "result")
    public void processUserInfo(Object result) {
        // 对用户信息进行匿名化处理
    }
    
    // 处理用户列表
    @AfterReturning(pointcut = "execution(...)", returning = "result")
    public void processUserList(Object result) {
        // 对用户列表进行K-匿名化处理
    }
}

// K-匿名化工具类
public class KAnonymityUtil {
    // 用户信息匿名化
    public static void anonymizeUserInfo(UserDTO user, KAnonymityConfig config) {
        // 根据不同策略进行匿名化
    }
    
    // 用户列表K-匿名化
    public static void anonymizeUserList(List<UserDTO> users, KAnonymityConfig config) {
        // 按K值分组处理
    }
}
```

### 前端状态管理
```javascript
// Pinia Store
export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref(null)

  // 用户信息初始化
  const initUserInfo = async () => {
    // 恢复登录状态
  }

  // 登录处理
  const handleLogin = async (credentials) => {
    // 登录和状态保存
  }

  return {
    token,
    userInfo,
    initUserInfo,
    handleLogin,
    // ...
  }
})
```

## 数据库设计

### 主要表结构
- user：用户表
- post：动态表
- comment：评论表
- post_like：点赞表
- friendship：好友关系表
- k_anonymity_config：K-匿名化配置表

## 环境要求

### 后端环境
- JDK 8+
- Maven 3.6+
- MySQL 8.0+

### 前端环境
- Node.js 16+
- npm 8+

## 启动方式

### 后端启动
1. 创建数据库并导入SQL脚本
social_system.sql

2. 修改配置文件
```yaml
# application.yml
spring:
  datasource:
    url: **********************************
    username: your_username  # 你的数据库用户名
    password: your_password  # 你的数据库密码

file:
  upload:
    path: D:\xxx\k-anonymity\backend\src\main\resources\files  # 文件上传路径
```

3. 启动项目
```bash
cd backend
mvn spring-boot:run
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 项目特点

1. 完整的社交系统功能实现
2. 基于K-匿名化的隐私保护
3. 前后端分离架构
4. AOP实现非侵入式隐私保护
5. 响应式设计，移动端适配
6. 优雅的UI设计和交互体验

## 注意事项

1. K-匿名化配置需要根据实际用户规模合理设置
2. 图片上传需要配置合适的存储方案
3. 生产环境部署需要配置HTTPS
4. 需要定期清理临时文件和日志
5. 建议定期备份数据库
