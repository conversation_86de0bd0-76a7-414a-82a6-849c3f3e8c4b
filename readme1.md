# 匿名社交网络系统设计文档

## 1. 技术选型

### 1.1 后端技术栈
- 核心框架：Spring Boot 2.7.x
- 安全框架：Spring Security + JWT
- 数据库：MySQL 8.0
- ORM框架：MyBatis-Plus 3.5.x
- 工具库：
  - Lombok：简化开发
  - Hutool：工具集合
  - Validation：参数校验
  - AOP：面向切面编程

### 1.2 前端技术栈
- 核心框架：Vue 3
- 状态管理：Pinia
- UI组件库：Element Plus
- 构建工具：Vite
- 工具库：
  - Axios：HTTP请求
  - ECharts：图表展示
  - dayjs：日期处理

### 1.3 核心算法
- K-匿名化算法：用于用户隐私保护
  - 基础策略：保留首尾字符
  - 增强策略：仅保留首字符
  - 严格策略：完全匿名化

## 2. 功能需求分析

### 2.1 用户模块
- 用户注册与登录
- 个人资料管理
- 密码修改
- 头像上传

### 2.2 社交模块
- 动态发布（支持文字和图片）
- 评论功能
- 点赞功能
- 好友关注

### 2.3 隐私保护模块
- K-匿名化配置管理
- 用户信息动态脱敏
- 多级匿名化策略

### 2.4 管理模块
- 用户管理
- 内容管理
- 数据统计
- 系统配置

## 3. 数据库设计

### 3.1 核心表结构
1. 用户表(user)
   - id: 主键
   - username: 用户名
   - password: 密码
   - nickname: 昵称
   - avatar: 头像URL
   - role: 角色
   - create_time: 创建时间
   - update_time: 更新时间
   - deleted: 逻辑删除

2. 动态表(post)
   - id: 主键
   - user_id: 用户ID
   - content: 内容
   - images: 图片URLs
   - like_count: 点赞数
   - comment_count: 评论数
   - create_time: 创建时间
   - update_time: 更新时间
   - deleted: 逻辑删除

3. 评论表(comment)
   - id: 主键
   - post_id: 动态ID
   - user_id: 用户ID
   - content: 评论内容
   - create_time: 创建时间
   - update_time: 更新时间
   - deleted: 逻辑删除

4. 好友关系表(friendship)
   - id: 主键
   - user_id: 用户ID
   - friend_id: 好友ID
   - create_time: 创建时间
   - update_time: 更新时间
   - deleted: 逻辑删除

5. K匿名配置表(k_anonymity_config)
   - id: 主键
   - k_value: K值
   - strategy: 策略
   - enabled: 是否启用
   - description: 描述
   - create_time: 创建时间
   - update_time: 更新时间
   - deleted: 逻辑删除

### 3.2 表关系
- 用户-动态：一对多
- 用户-评论：一对多
- 动态-评论：一对多
- 用户-好友：多对多 