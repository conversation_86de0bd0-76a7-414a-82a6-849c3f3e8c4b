import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import DashboardView from '../views/DashboardView.vue'
import KAnonymityView from '../views/KAnonymityView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: { requiresAuth: true, useMainLayout: true }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true, useMainLayout: true }
    },
    {
      path: '/friends',
      name: 'friends',
      component: () => import('../views/FriendsView.vue'),
      meta: { requiresAuth: true, useMainLayout: true }
    },
    {
      path: '/privacy',
      name: 'privacy',
      component: () => import('../views/PrivacySettingsView.vue'),
      meta: { requiresAuth: true, useMainLayout: true }
    },
    {
      path: '/admin/dashboard',
      name: 'dashboard',
      component: DashboardView,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        useMainLayout: true
      }
    },
    {
      path: '/admin/k-anonymity',
      name: 'k-anonymity',
      component: KAnonymityView,
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        useMainLayout: true
      }
    },
    {
      path: '/admin/sensitive-word',
      name: 'sensitive-word',
      component: () => import('../views/SensitiveWordView.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        useMainLayout: true
      }
    },
    {
      path: '/announcement',
      name: 'announcement',
      component: () => import('../views/AnnouncementView.vue'),
      meta: {
        requiresAuth: true,
        useMainLayout: true
      }
    },
    {
      path: '/announcement/:id',
      name: 'announcement-detail',
      component: () => import('../views/AnnouncementDetailView.vue'),
      meta: {
        requiresAuth: true,
        useMainLayout: true
      }
    },
    {
      path: '/admin/announcement',
      name: 'announcement-manage',
      component: () => import('../views/AnnouncementManageView.vue'),
      meta: {
        requiresAuth: true,
        requiresAdmin: true,
        useMainLayout: true
      }
    }
  ]
})

router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router