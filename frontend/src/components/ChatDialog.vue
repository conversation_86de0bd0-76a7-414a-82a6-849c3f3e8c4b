<template>
  <el-dialog
      v-model="dialogVisible"
      :title="'与 ' + targetUser?.nickname + ' 的对话'"
      width="600px"
      class="chat-dialog"
  >
    <div class="chat-container">
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="loading" class="loading-container">
          <el-spinner/>
        </div>
        <template v-else>
          <div v-if="messages.length === 0" class="empty-tip">
            暂无消息记录
          </div>
          <div
              v-for="message in messages"
              :key="message.id"
              :class="['message-item', message.fromUserId === currentUserId ? 'sent' : 'received']"
          >
            <div class="message-avatar">
              <el-avatar :size="32"
                         :src="message.fromUserId === currentUserId ? currentUser?.avatar : targetUser?.avatar">
                {{ message.fromUserId === currentUserId ? currentUser?.nickname?.[0] : targetUser?.nickname?.[0] }}
              </el-avatar>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ formatTime(message.createTime) }}</div>
            </div>
          </div>
        </template>
      </div>

      <div class="chat-input">
        <el-input
            v-model="messageText"
            type="textarea"
            :rows="3"
            placeholder="输入消息..."
            @keyup.enter.exact.prevent="handleSend"
        />
        <div class="input-actions">
          <el-button type="primary" :disabled="!messageText.trim()" @click="handleSend">
            发送
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import {ref, watch, nextTick, onMounted, onUnmounted} from 'vue'
import {useUserStore} from '../stores/user'
import {ElMessage} from 'element-plus'
import {getChatHistory, sendMessage} from '../api'
import dayjs from 'dayjs'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  targetUser: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update:visible'])

const userStore = useUserStore()
const currentUserId = userStore.userInfo?.id
const currentUser = userStore.userInfo

const dialogVisible = ref(props.visible)
const messageText = ref('')
const messages = ref([])
const loading = ref(false)
const messagesContainer = ref(null)
const pageNum = ref(1)
const pageSize = 20
let messagePollingInterval = null

// 监听对话框显示状态
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    loadMessages()
    startMessagePolling()
  } else {
    stopMessagePolling()
  }
})

watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    stopMessagePolling()
  }
})

// 加载消息历史
const loadMessages = async () => {
  if (!props.targetUser?.id) return

  try {
    loading.value = true
    const response = await getChatHistory(props.targetUser.id, pageNum.value, pageSize)
    messages.value = response.data.records.reverse()

    await nextTick()
    scrollToBottom()
  } catch (error) {
    ElMessage.error('获取消息记录失败')
  } finally {
    loading.value = false
  }
}

// 发送消息
const handleSend = async () => {
  if (!messageText.value.trim()) return

  try {
    const response = await sendMessage({
      toUserId: props.targetUser.id,
      content: messageText.value,
      messageType: 'TEXT'
    })

    messages.value.push(response.data)
    messageText.value = ''

    await nextTick()
    scrollToBottom()
  } catch (error) {
    ElMessage.error('发送失败')
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 轮询新消息
const startMessagePolling = () => {
  messagePollingInterval = setInterval(async () => {
    try {
      const response = await getChatHistory(props.targetUser.id, 1, pageSize)
      const newMessages = response.data.records.reverse()

      if (newMessages.length > messages.value.length) {
        messages.value = newMessages
        await nextTick()
        scrollToBottom()
      }
    } catch (error) {
      console.error('轮询消息失败:', error)
    }
  }, 5000) // 每5秒轮询一次
}

const stopMessagePolling = () => {
  if (messagePollingInterval) {
    clearInterval(messagePollingInterval)
    messagePollingInterval = null
  }
}

onMounted(() => {
  if (props.visible) {
    loadMessages()
    startMessagePolling()
  }
})

onUnmounted(() => {
  stopMessagePolling()
})
</script>

<style scoped>
.chat-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f7fa;
}

.message-item {
  display: flex;
  margin-bottom: 20px;
  align-items: flex-start;
}

.message-item.sent {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 10px;
}

.message-content {
  max-width: 70%;
}

.message-text {
  padding: 10px 15px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-break: break-word;
}

.message-item.sent .message-text {
  background-color: #95ec69;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
  text-align: right;
}

.chat-input {
  padding: 20px;
  border-top: 1px solid #eee;
  background-color: #fff;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.empty-tip {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style> 