<template>
  <div class="app-container">
    <!-- 左侧导航栏 -->
    <div class="sidebar">
      <div class="logo-container">
        <router-link to="/" class="logo">
          <h1>匿名社交</h1>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :router="true"
          :collapse="isCollapse"
      >
        <el-menu-item index="/">
          <el-icon>
            <HomeFilled/>
          </el-icon>
          <template #title>首页</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo?.role === 'USER'" index="/profile">
          <el-icon>
            <User/>
          </el-icon>
          <template #title>个人资料</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo?.role === 'USER'" index="/friends">
          <el-icon>
            <UserFilled/>
          </el-icon>
          <template #title>关注列表</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo" index="/privacy">
          <el-icon>
            <Lock/>
          </el-icon>
          <template #title>隐私设置</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo?.role === 'ADMIN'" index="/admin/dashboard">
          <el-icon>
            <Odometer/>
          </el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo?.role === 'ADMIN'" index="/admin/k-anonymity">
          <el-icon>
            <Setting/>
          </el-icon>
          <template #title>匿名配置</template>
        </el-menu-item>

        <el-menu-item v-if="userStore.userInfo?.role === 'ADMIN'" index="/admin/sensitive-word">
          <el-icon>
            <Warning/>
          </el-icon>
          <template #title>敏感词管理</template>
        </el-menu-item>


      </el-menu>

      <!-- 折叠按钮 -->
      <div class="collapse-btn" @click="toggleCollapse">
        <el-icon v-if="isCollapse">
          <Expand/>
        </el-icon>
        <el-icon v-else>
          <Fold/>
        </el-icon>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部用户信息栏 -->
      <div class="top-header">
        <div class="user-info">
          <el-dropdown @command="handleCommand">
            <div class="user-dropdown">
              <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                {{ getDisplayName(userStore.userInfo)?.[0] || userStore.userInfo?.username?.[0] }}
              </el-avatar>
              <span class="username">{{ getDisplayName(userStore.userInfo) }}</span>
              <el-icon>
                <arrow-down/>
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="userStore.userInfo?.role === 'USER'" command="profile">
                  <el-icon>
                    <user/>
                  </el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item v-if="userStore.userInfo?.role === 'USER'" command="friends">
                  <el-icon>
                    <user-filled/>
                  </el-icon>
                  关注列表
                </el-dropdown-item>
                <el-dropdown-item v-if="userStore.userInfo?.role === 'ADMIN'" command="dashboard">
                  <el-icon>
                    <Odometer/>
                  </el-icon>
                  仪表盘
                </el-dropdown-item>
                <el-dropdown-item v-if="userStore.userInfo?.role === 'ADMIN'" command="k-anonymity">
                  <el-icon>
                    <Setting/>
                  </el-icon>
                  匿名配置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon>
                    <switch-button/>
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div class="content-area">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, computed} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {useUserStore} from '../stores/user'
import {ElMessageBox} from 'element-plus'
import { getDisplayName } from '../utils/anonymize'
import {
  HomeFilled,
  User,
  UserFilled,
  SwitchButton,
  Odometer,
  Setting,
  Expand,
  Fold,
  ArrowDown,
  Lock,
  Warning
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 控制侧边栏折叠状态
const isCollapse = ref(false)
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'friends':
      router.push('/friends')
      break
    case 'dashboard':
      router.push('/admin/dashboard')
      break
    case 'k-anonymity':
      router.push('/admin/k-anonymity')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout()
    router.push('/login')
  }).catch(() => {
  })
}
</script>

<style scoped>
.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.sidebar {
  display: flex;
  flex-direction: column;
  width: 240px;
  height: 100%;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 1000;
  position: relative;
  overflow-x: hidden;
}

.sidebar.collapsed {
  width: 64px;
}

.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  border-bottom: 1px solid #f0f0f0;
}

.logo {
  text-decoration: none;
}

.logo h1 {
  margin: 0;
  font-size: 20px;
  color: var(--primary-color, #409eff);
  white-space: nowrap;
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
  background-color: rgba(64, 158, 255, 0.05);
}

.user-name {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90%;
  text-align: center;
}

.sidebar-menu {
  flex: 1;
  border-right: none;
  font-size: 15px;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  transition: all 0.3s;
}

:deep(.el-menu-item.is-active) {
  background-color: rgba(64, 158, 255, 0.1);
  border-right: 3px solid var(--primary-color);
}

:deep(.el-menu-item:hover) {
  background-color: rgba(64, 158, 255, 0.05);
}

.collapse-btn {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-top: 1px solid #f0f0f0;
  color: #909399;
}

.collapse-btn:hover {
  background-color: #f5f7fa;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  transition: all 0.3s ease;
  overflow: hidden;
}

.top-header {
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 20px;
  z-index: 10;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown:hover {
  background-color: var(--background-color);
}

.username {
  margin: 0 8px;
  color: var(--text-color);
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .sidebar {
    width: 64px;
  }

  .user-name {
    display: none;
  }

  .logo h1 {
    display: none;
  }
}
</style>
