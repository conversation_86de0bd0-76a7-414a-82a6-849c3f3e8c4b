<template>
  <el-dialog
    v-model="dialogVisible"
    title="用户资料"
    width="500px"
    :before-close="handleClose"
  >
    <div class="user-profile-container" v-loading="loading">
      <div class="user-header">
        <el-avatar :size="80" :src="userInfo.avatar">
          {{ getDisplayName(userInfo)?.[0] || userInfo.username?.[0] }}
        </el-avatar>
        <h2 class="user-nickname">{{ getDisplayName(userInfo) }}</h2>
      </div>

      <el-divider />

      <div class="user-info-list">
        <div class="info-item" v-if="userInfo.realName">
          <span class="info-label">真实姓名：</span>
          <span class="info-value">{{ userInfo.realName }}</span>
        </div>

        <div class="info-item" v-if="userInfo.phone">
          <span class="info-label">手机号码：</span>
          <span class="info-value">{{ userInfo.phone }}</span>
        </div>

        <div class="info-item" v-if="userInfo.address">
          <span class="info-label">家庭住址：</span>
          <span class="info-value">{{ userInfo.address }}</span>
        </div>
      </div>

      <div class="action-buttons" v-if="!isCurrentUser">
        <el-button
          type="primary"
          :loading="followLoading"
          @click="handleFollow"
          v-if="!isFollowing"
        >
          关注
        </el-button>
        <el-button
          type="danger"
          :loading="followLoading"
          @click="handleUnfollow"
          v-else
        >
          取消关注
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getUserInfo, addFriend, deleteFriend, hasFollowed } from '../api'
import { useUserStore } from '../stores/user'
import { getDisplayName, anonymizeUserInfo } from '../utils/anonymize'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'close'])

const userStore = useUserStore()
const dialogVisible = ref(props.visible)
const loading = ref(false)
const followLoading = ref(false)
const isFollowing = ref(false)
const userInfo = ref({})

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.userId) {
    fetchUserInfo()
    checkFollowStatus()
  }
})

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    emit('close')
  }
})

// 判断是否是当前用户
const isCurrentUser = computed(() => {
  return userStore.userInfo?.id === props.userId
})

// 获取用户信息
const fetchUserInfo = async () => {
  if (!props.userId) return

  try {
    loading.value = true
    const response = await getUserInfo(props.userId)
    // 对用户信息进行匿名化处理
    userInfo.value = anonymizeUserInfo(response.data)
  } catch (error) {
    console.error('获取用户信息失败', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 检查是否已关注
const checkFollowStatus = async () => {
  if (!props.userId || isCurrentUser.value) return

  try {
    const response = await hasFollowed(props.userId)
    isFollowing.value = response.data
  } catch (error) {
    console.error('检查关注状态失败', error)
  }
}

// 关注用户
const handleFollow = async () => {
  if (!props.userId) return

  try {
    followLoading.value = true
    await addFriend(props.userId)
    isFollowing.value = true
    ElMessage.success('关注成功')
  } catch (error) {
    console.error('关注失败', error)
    ElMessage.error('关注失败')
  } finally {
    followLoading.value = false
  }
}

// 取消关注
const handleUnfollow = async () => {
  if (!props.userId) return

  try {
    followLoading.value = true
    await deleteFriend(props.userId)
    isFollowing.value = false
    ElMessage.success('已取消关注')
  } catch (error) {
    console.error('取消关注失败', error)
    ElMessage.error('取消关注失败')
  } finally {
    followLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
.user-profile-container {
  padding: 10px;
}

.user-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.user-nickname {
  margin-top: 10px;
  font-size: 18px;
  color: var(--text-color);
}

.user-info-list {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
  font-size: 14px;
}

.info-label {
  width: 80px;
  color: var(--text-color-secondary);
  flex-shrink: 0;
}

.info-value {
  color: var(--text-color);
  word-break: break-all;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
