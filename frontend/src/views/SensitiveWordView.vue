<template>
  <MainLayout>
    <div class="sensitive-word-container">
      <el-card class="sensitive-word-card">
        <template #header>
          <div class="card-header">
            <h2 class="card-title">敏感词管理</h2>
            <div class="header-actions">
              <el-button type="primary" @click="showAddDialog">添加敏感词</el-button>
              <el-button type="success" @click="showBatchImportDialog">批量导入</el-button>
              <el-button type="warning" @click="showTestDialog">测试过滤</el-button>
            </div>
          </div>
        </template>

        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索敏感词"
            clearable
            @clear="handleSearch"
            style="width: 300px; margin-right: 10px;"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>

          <el-select v-model="searchCategory" placeholder="分类" clearable @change="handleSearch" style="width: 150px;">
            <el-option label="政治" value="POLITICS" />
            <el-option label="色情" value="PORN" />
            <el-option label="辱骂" value="ABUSE" />
            <el-option label="其他" value="OTHERS" />
          </el-select>
        </div>

        <!-- 敏感词列表 -->
        <el-table
          v-loading="loading"
          :data="sensitiveWords"
          border
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column prop="word" label="敏感词" min-width="150" />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.category === 'POLITICS'" type="danger">政治</el-tag>
              <el-tag v-else-if="scope.row.category === 'PORN'" type="warning">色情</el-tag>
              <el-tag v-else-if="scope.row.category === 'ABUSE'" type="info">辱骂</el-tag>
              <el-tag v-else type="success">其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="level" label="级别" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.level === 1" type="info">低</el-tag>
              <el-tag v-else-if="scope.row.level === 2" type="warning">中</el-tag>
              <el-tag v-else-if="scope.row.level === 3" type="danger">高</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 添加/编辑敏感词对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑敏感词' : '添加敏感词'"
        width="500px"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="敏感词" prop="word">
            <el-input v-model="form.word" placeholder="请输入敏感词" />
          </el-form-item>
          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%;">
              <el-option label="政治" value="POLITICS" />
              <el-option label="色情" value="PORN" />
              <el-option label="辱骂" value="ABUSE" />
              <el-option label="其他" value="OTHERS" />
            </el-select>
          </el-form-item>
          <el-form-item label="级别" prop="level">
            <el-select v-model="form.level" placeholder="请选择级别" style="width: 100%;">
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 批量导入对话框 -->
      <el-dialog
        v-model="batchImportDialogVisible"
        title="批量导入敏感词"
        width="600px"
      >
        <el-form
          ref="batchFormRef"
          :model="batchForm"
          :rules="batchRules"
          label-width="80px"
        >
          <el-form-item label="敏感词" prop="words">
            <el-input
              v-model="batchForm.words"
              type="textarea"
              :rows="10"
              placeholder="请输入敏感词，每行一个"
            />
          </el-form-item>
          <el-form-item label="分类" prop="category">
            <el-select v-model="batchForm.category" placeholder="请选择分类" style="width: 100%;">
              <el-option label="政治" value="POLITICS" />
              <el-option label="色情" value="PORN" />
              <el-option label="辱骂" value="ABUSE" />
              <el-option label="其他" value="OTHERS" />
            </el-select>
          </el-form-item>
          <el-form-item label="级别" prop="level">
            <el-select v-model="batchForm.level" placeholder="请选择级别" style="width: 100%;">
              <el-option label="低" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="高" :value="3" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="batchImportDialogVisible = false">取消</el-button>
            <el-button type="primary" :loading="batchSubmitting" @click="handleBatchImport">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 测试过滤对话框 -->
      <el-dialog
        v-model="testDialogVisible"
        title="测试敏感词过滤"
        width="600px"
      >
        <el-form>
          <el-form-item label="原文本">
            <el-input
              v-model="testText"
              type="textarea"
              :rows="5"
              placeholder="请输入要测试的文本"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :loading="testing" @click="handleTest">测试</el-button>
          </el-form-item>
          <el-form-item label="过滤结果" v-if="filteredText">
            <el-input
              v-model="filteredText"
              type="textarea"
              :rows="5"
              readonly
            />
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import MainLayout from '../components/MainLayout.vue'
import { getSensitiveWordPage, addSensitiveWord, updateSensitiveWord, deleteSensitiveWord, batchImportSensitiveWords, testSensitiveWordFilter } from '../api'

// 数据
const loading = ref(false)
const submitting = ref(false)
const batchSubmitting = ref(false)
const testing = ref(false)
const sensitiveWords = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const searchCategory = ref('')

// 对话框
const dialogVisible = ref(false)
const batchImportDialogVisible = ref(false)
const testDialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)
const batchFormRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  word: '',
  category: 'OTHERS',
  level: 1
})

const batchForm = ref({
  words: '',
  category: 'OTHERS',
  level: 1
})

const testText = ref('')
const filteredText = ref('')

// 表单验证规则
const rules = {
  word: [
    { required: true, message: '请输入敏感词', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择级别', trigger: 'change' }
  ]
}

const batchRules = {
  words: [
    { required: true, message: '请输入敏感词', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  level: [
    { required: true, message: '请选择级别', trigger: 'change' }
  ]
}

// API 请求
const fetchSensitiveWords = async () => {
  try {
    loading.value = true
    const response = await getSensitiveWordPage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      category: searchCategory.value
    })
    sensitiveWords.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取敏感词列表失败', error)
    ElMessage.error('获取敏感词列表失败')
  } finally {
    loading.value = false
  }
}

// 添加敏感词
const handleAddSensitiveWord = async (data) => {
  try {
    submitting.value = true
    await addSensitiveWord(data)
    ElMessage.success('添加成功')
    dialogVisible.value = false
    fetchSensitiveWords()
  } catch (error) {
    console.error('添加敏感词失败', error)
    ElMessage.error(error.response?.data?.message || '添加敏感词失败')
  } finally {
    submitting.value = false
  }
}

// 更新敏感词
const handleUpdateSensitiveWord = async (data) => {
  try {
    submitting.value = true
    await updateSensitiveWord(data)
    ElMessage.success('更新成功')
    dialogVisible.value = false
    fetchSensitiveWords()
  } catch (error) {
    console.error('更新敏感词失败', error)
    ElMessage.error(error.response?.data?.message || '更新敏感词失败')
  } finally {
    submitting.value = false
  }
}

// 删除敏感词
const handleDeleteSensitiveWord = async (id) => {
  try {
    await deleteSensitiveWord(id)
    ElMessage.success('删除成功')
    fetchSensitiveWords()
  } catch (error) {
    console.error('删除敏感词失败', error)
    ElMessage.error('删除敏感词失败')
  }
}

// 批量导入敏感词
const handleBatchImportSensitiveWords = async (data) => {
  try {
    batchSubmitting.value = true
    const response = await batchImportSensitiveWords(data)
    const successCount = response.data.count
    const totalCount = data.words.length
    const failedCount = totalCount - successCount

    if (failedCount > 0) {
      ElMessage({
        message: `导入完成: 成功 ${successCount} 个, 失败 ${failedCount} 个 (可能是重复或已存在)`,
        type: 'warning'
      })
    } else {
      ElMessage.success(`成功导入 ${successCount} 个敏感词`)
    }
    batchImportDialogVisible.value = false
    fetchSensitiveWords()
  } catch (error) {
    console.error('批量导入敏感词失败', error)
    ElMessage.error('批量导入敏感词失败')
  } finally {
    batchSubmitting.value = false
  }
}

// 测试敏感词过滤
const handleTestSensitiveWordFilter = async (text) => {
  try {
    testing.value = true
    const response = await testSensitiveWordFilter({ text })
    filteredText.value = response.data.filtered
  } catch (error) {
    console.error('测试敏感词过滤失败', error)
    ElMessage.error('测试敏感词过滤失败')
  } finally {
    testing.value = false
  }
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  fetchSensitiveWords()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchSensitiveWords()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchSensitiveWords()
}

const showAddDialog = () => {
  isEdit.value = false
  form.value = {
    id: null,
    word: '',
    category: 'OTHERS',
    level: 1
  }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这个敏感词吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    handleDeleteSensitiveWord(row.id)
  }).catch(() => {})
}

const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      if (isEdit.value) {
        await handleUpdateSensitiveWord(form.value)
      } else {
        await handleAddSensitiveWord(form.value)
      }
    }
  })
}

const showBatchImportDialog = () => {
  batchForm.value = {
    words: '',
    category: 'OTHERS',
    level: 1
  }
  batchImportDialogVisible.value = true
}

const handleBatchImport = async () => {
  if (!batchFormRef.value) return

  await batchFormRef.value.validate(async (valid) => {
    if (valid) {
      // 将文本按行分割成数组
      const words = batchForm.value.words
        .split('\n')
        .map(word => word.trim())
        .filter(word => word)

      if (words.length === 0) {
        ElMessage.warning('请输入至少一个敏感词')
        return
      }

      await handleBatchImportSensitiveWords({
        words,
        category: batchForm.value.category,
        level: batchForm.value.level
      })
    }
  })
}

const showTestDialog = () => {
  testText.value = ''
  filteredText.value = ''
  testDialogVisible.value = true
}

const handleTest = () => {
  if (!testText.value) {
    ElMessage.warning('请输入要测试的文本')
    return
  }

  handleTestSensitiveWordFilter(testText.value)
}

// 生命周期钩子
onMounted(() => {
  fetchSensitiveWords()
})
</script>

<style scoped>
.sensitive-word-container {
  max-width: 1200px;
  margin: 0 auto;
}

.sensitive-word-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-area {
  display: flex;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
