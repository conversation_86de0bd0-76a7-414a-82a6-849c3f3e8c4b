<template>
  <div class="page-container login-container">
    <div class="form-container">
      <el-card class="login-card">
        <h2 class="form-title">欢迎登录</h2>
        <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="rules"
            label-position="top"
            @submit.prevent="handleLogin"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                :prefix-icon="User"
                @keyup.enter="handleLogin"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
                @keyup.enter="handleLogin"
            />
          </el-form-item>

          <div class="form-footer">
            <el-button
                type="primary"
                :loading="loading"
                class="submit-btn"
                @click="handleLogin"
            >
              登录
            </el-button>
            <div class="form-links">
              <el-link type="primary" @click="$router.push('/register')">
                注册账号
              </el-link>
            </div>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '../stores/user'
import {ElMessage} from 'element-plus'
import {User, Lock} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref(null)

const loginForm = ref({
  username: '',
  password: ''
})

const loading = ref(false)

const rules = {
  username: [
    {required: true, message: '请输入用户名', trigger: 'blur'},
    {min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur'}
  ],
  password: [
    {required: true, message: '请输入密码', trigger: 'blur'},
    {min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur'}
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true
    await userStore.handleLogin(loginForm.value)
    ElMessage.success('登录成功')
    router.push('/')
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1989fa 100%);
}

.login-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.form-footer {
  margin-top: 30px;
}

.submit-btn {
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
}

.form-links {
  margin-top: 15px;
  text-align: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  padding: 8px 15px;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1989fa 100%);
  border: none;
}
</style> 