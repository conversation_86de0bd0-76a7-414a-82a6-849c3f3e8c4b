<template>
  <MainLayout>
    <div class="announcement-container">
      <el-card class="announcement-card">
        <template #header>
          <div class="card-header">
            <h2 class="card-title">咨询公告</h2>
          </div>
        </template>
        
        <!-- 搜索和筛选区域 -->
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索公告标题或内容"
            clearable
            @clear="handleSearch"
            style="width: 300px; margin-right: 10px;"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
          
          <el-select v-model="searchCategory" placeholder="分类" clearable @change="handleSearch" style="width: 150px;">
            <el-option label="热点新闻" value="NEWS" />
            <el-option label="娱乐周刊" value="ENTERTAINMENT" />
            <el-option label="民生事件" value="LIVELIHOOD" />
            <el-option label="惠民政策" value="POLICY" />
          </el-select>
        </div>
        
        <!-- 公告列表 -->
        <div class="announcement-list" v-loading="loading">
          <div
            v-for="announcement in announcements"
            :key="announcement.id"
            class="announcement-item"
            @click="goToDetail(announcement.id)"
          >
            <div class="announcement-header">
              <div class="announcement-meta">
                <el-tag v-if="announcement.isTop" type="danger" size="small">置顶</el-tag>
                <el-tag :type="getCategoryTagType(announcement.category)" size="small">
                  {{ getCategoryName(announcement.category) }}
                </el-tag>
                <span class="announcement-time">{{ announcement.createTime }}</span>
              </div>
            </div>
            
            <h3 class="announcement-title">{{ announcement.title }}</h3>
            
            <div class="announcement-content">
              {{ getContentPreview(announcement.content) }}
            </div>
            
            <div class="announcement-footer">
              <div class="announcement-author">
                <el-avatar :size="24" :src="announcement.author?.avatar">
                  {{ getDisplayName(announcement.author)?.[0] || 'A' }}
                </el-avatar>
                <span class="author-name">{{ getDisplayName(announcement.author) || '管理员' }}</span>
              </div>
              
              <div class="announcement-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ announcement.viewCount }}
                </span>
                <span class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  {{ announcement.commentCount }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ announcement.likeCount }}
                </span>
              </div>
            </div>
          </div>
          
          <div v-if="announcements.length === 0 && !loading" class="empty-state">
            <el-empty description="暂无公告" />
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, View, ChatDotRound, Star } from '@element-plus/icons-vue'
import MainLayout from '../components/MainLayout.vue'
import { getAnnouncementPage } from '../api'
import { getDisplayName } from '../utils/anonymize'

const router = useRouter()

// 数据
const loading = ref(false)
const announcements = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const searchCategory = ref('')

// 获取公告列表
const fetchAnnouncements = async () => {
  try {
    loading.value = true
    const response = await getAnnouncementPage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      category: searchCategory.value
    })
    announcements.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取公告列表失败', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  fetchAnnouncements()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchAnnouncements()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchAnnouncements()
}

const goToDetail = (id) => {
  router.push(`/announcement/${id}`)
}

// 工具函数
const getCategoryName = (category) => {
  const categoryMap = {
    NEWS: '热点新闻',
    ENTERTAINMENT: '娱乐周刊',
    LIVELIHOOD: '民生事件',
    POLICY: '惠民政策'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category) => {
  const typeMap = {
    NEWS: 'danger',
    ENTERTAINMENT: 'warning',
    LIVELIHOOD: 'info',
    POLICY: 'success'
  }
  return typeMap[category] || 'info'
}

const getContentPreview = (content) => {
  if (!content) return ''
  return content.length > 150 ? content.substring(0, 150) + '...' : content
}

// 生命周期钩子
onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.announcement-container {
  max-width: 1200px;
  margin: 0 auto;
}

.announcement-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.search-area {
  display: flex;
  margin-bottom: 20px;
  gap: 10px;
}

.announcement-list {
  min-height: 400px;
}

.announcement-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: #fff;
}

.announcement-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.announcement-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.announcement-time {
  color: #909399;
  font-size: 12px;
}

.announcement-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.announcement-content {
  color: #606266;
  line-height: 1.6;
  margin-bottom: 16px;
}

.announcement-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.announcement-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
  color: #606266;
}

.announcement-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
