<template>
  <MainLayout>
    <div class="privacy-settings-container">
      <el-card class="privacy-card">
        <template #header>
          <h2 class="card-title">隐私设置</h2>
        </template>
        
        <el-form :model="privacyForm" label-position="top" class="privacy-form">
          <el-divider content-position="left">个人资料隐私</el-divider>
          
          <el-form-item label="个人资料可见性">
            <el-select v-model="privacyForm.profileVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
            <div class="privacy-hint">控制昵称、头像等基本资料的可见性</div>
          </el-form-item>
          
          <el-form-item label="真实姓名可见性">
            <el-select v-model="privacyForm.realNameVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="手机号码可见性">
            <el-select v-model="privacyForm.phoneVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="家庭住址可见性">
            <el-select v-model="privacyForm.addressVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>
          
          <el-divider content-position="left">社交隐私</el-divider>
          
          <el-form-item label="动态可见性">
            <el-select v-model="privacyForm.postsVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
            <div class="privacy-hint">控制您发布的动态的可见性</div>
          </el-form-item>
          
          <el-form-item label="好友列表可见性">
            <el-select v-model="privacyForm.friendsListVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="在线状态可见性">
            <el-select v-model="privacyForm.onlineStatusVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" :loading="loading" @click="saveSettings">保存设置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import MainLayout from '../components/MainLayout.vue'
import { getPrivacySettings, updatePrivacySettings } from '../api'

const loading = ref(false)

// 隐私设置表单
const privacyForm = ref({
  profileVisibility: 'PUBLIC',
  realNameVisibility: 'FRIENDS_ONLY',
  phoneVisibility: 'PRIVATE',
  addressVisibility: 'PRIVATE',
  postsVisibility: 'PUBLIC',
  friendsListVisibility: 'PUBLIC',
  onlineStatusVisibility: 'PUBLIC'
})

// 获取当前隐私设置
const fetchPrivacySettings = async () => {
  try {
    loading.value = true
    const response = await getPrivacySettings()
    const settings = response.data
    
    // 更新表单数据
    privacyForm.value = {
      profileVisibility: settings.profileVisibility,
      realNameVisibility: settings.realNameVisibility,
      phoneVisibility: settings.phoneVisibility,
      addressVisibility: settings.addressVisibility,
      postsVisibility: settings.postsVisibility,
      friendsListVisibility: settings.friendsListVisibility,
      onlineStatusVisibility: settings.onlineStatusVisibility
    }
  } catch (error) {
    ElMessage.error('获取隐私设置失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 保存隐私设置
const saveSettings = async () => {
  try {
    loading.value = true
    await updatePrivacySettings(privacyForm.value)
    ElMessage.success('隐私设置已更新')
  } catch (error) {
    ElMessage.error('保存隐私设置失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 页面加载时获取隐私设置
onMounted(() => {
  fetchPrivacySettings()
})
</script>

<style scoped>
.privacy-settings-container {
  max-width: 800px;
  margin: 0 auto;
}

.privacy-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.privacy-form {
  padding: 10px;
}

.privacy-select {
  width: 100%;
  max-width: 300px;
}

.privacy-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.el-divider {
  margin: 20px 0;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: var(--primary-color);
}
</style>
