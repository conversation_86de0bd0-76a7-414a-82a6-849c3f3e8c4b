<template>
  <MainLayout>
    <div class="announcement-manage-container">
      <el-card class="announcement-manage-card">
        <template #header>
          <div class="card-header">
            <h2 class="card-title">公告管理</h2>
            <el-button type="primary" @click="showAddDialog">发布公告</el-button>
          </div>
        </template>
        
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索公告标题或内容"
            clearable
            @clear="handleSearch"
            style="width: 300px; margin-right: 10px;"
          >
            <template #append>
              <el-button @click="handleSearch">
                <el-icon><Search /></el-icon>
              </el-button>
            </template>
          </el-input>
          
          <el-select v-model="searchCategory" placeholder="分类" clearable @change="handleSearch" style="width: 150px; margin-right: 10px;">
            <el-option label="热点新闻" value="NEWS" />
            <el-option label="娱乐周刊" value="ENTERTAINMENT" />
            <el-option label="民生事件" value="LIVELIHOOD" />
            <el-option label="惠民政策" value="POLICY" />
          </el-select>
          
          <el-select v-model="searchStatus" placeholder="状态" clearable @change="handleSearch" style="width: 120px;">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </div>
        
        <!-- 公告列表 -->
        <el-table
          v-loading="loading"
          :data="announcements"
          border
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
          <el-table-column prop="category" label="分类" width="120">
            <template #default="scope">
              <el-tag :type="getCategoryTagType(scope.row.category)">
                {{ getCategoryName(scope.row.category) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.status === 'PUBLISHED'" type="success">已发布</el-tag>
              <el-tag v-else-if="scope.row.status === 'DRAFT'" type="warning">草稿</el-tag>
              <el-tag v-else-if="scope.row.status === 'ARCHIVED'" type="info">已归档</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isTop" label="置顶" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.isTop" type="danger" size="small">置顶</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="viewCount" label="浏览" width="80" />
          <el-table-column prop="likeCount" label="点赞" width="80" />
          <el-table-column prop="commentCount" label="评论" width="80" />
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="info" size="small" @click="handleView(scope.row)">查看</el-button>
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      
      <!-- 添加/编辑公告对话框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑公告' : '发布公告'"
        width="800px"
        :before-close="handleDialogClose"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入公告标题" maxlength="200" show-word-limit />
          </el-form-item>
          <el-form-item label="分类" prop="category">
            <el-select v-model="form.category" placeholder="请选择分类" style="width: 100%;">
              <el-option label="热点新闻" value="NEWS" />
              <el-option label="娱乐周刊" value="ENTERTAINMENT" />
              <el-option label="民生事件" value="LIVELIHOOD" />
              <el-option label="惠民政策" value="POLICY" />
            </el-select>
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="form.content"
              type="textarea"
              :rows="10"
              placeholder="请输入公告内容"
              maxlength="5000"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
              <el-option label="草稿" value="DRAFT" />
              <el-option label="已发布" value="PUBLISHED" />
              <el-option label="已归档" value="ARCHIVED" />
            </el-select>
          </el-form-item>
          <el-form-item label="置顶">
            <el-switch v-model="form.isTop" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="handleDialogClose">取消</el-button>
            <el-button type="primary" :loading="submitting" @click="handleSubmit">确定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import MainLayout from '../components/MainLayout.vue'
import { 
  getAnnouncementAdminPage, 
  createAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement 
} from '../api'

const router = useRouter()

// 数据
const loading = ref(false)
const submitting = ref(false)
const announcements = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const searchCategory = ref('')
const searchStatus = ref('')

// 对话框
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)

// 表单数据
const form = ref({
  id: null,
  title: '',
  content: '',
  category: 'NEWS',
  status: 'PUBLISHED',
  isTop: false
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' },
    { min: 1, max: 5000, message: '长度在 1 到 5000 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取公告列表
const fetchAnnouncements = async () => {
  try {
    loading.value = true
    const response = await getAnnouncementAdminPage({
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      category: searchCategory.value,
      status: searchStatus.value
    })
    announcements.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取公告列表失败', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  fetchAnnouncements()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchAnnouncements()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchAnnouncements()
}

const showAddDialog = () => {
  isEdit.value = false
  form.value = {
    id: null,
    title: '',
    content: '',
    category: 'NEWS',
    status: 'PUBLISHED',
    isTop: false
  }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  form.value = { ...row }
  dialogVisible.value = true
}

const handleView = (row) => {
  router.push(`/announcement/${row.id}`)
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除这个公告吗？删除后将无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteAnnouncement(row.id)
      ElMessage.success('删除成功')
      fetchAnnouncements()
    } catch (error) {
      console.error('删除公告失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        submitting.value = true
        if (isEdit.value) {
          await updateAnnouncement(form.value)
          ElMessage.success('更新成功')
        } else {
          await createAnnouncement(form.value)
          ElMessage.success('发布成功')
        }
        dialogVisible.value = false
        fetchAnnouncements()
      } catch (error) {
        console.error('操作失败', error)
        ElMessage.error(error.response?.data?.message || '操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

const handleDialogClose = () => {
  dialogVisible.value = false
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 工具函数
const getCategoryName = (category) => {
  const categoryMap = {
    NEWS: '热点新闻',
    ENTERTAINMENT: '娱乐周刊',
    LIVELIHOOD: '民生事件',
    POLICY: '惠民政策'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category) => {
  const typeMap = {
    NEWS: 'danger',
    ENTERTAINMENT: 'warning',
    LIVELIHOOD: 'info',
    POLICY: 'success'
  }
  return typeMap[category] || 'info'
}

// 生命周期钩子
onMounted(() => {
  fetchAnnouncements()
})
</script>

<style scoped>
.announcement-manage-container {
  max-width: 1400px;
  margin: 0 auto;
}

.announcement-manage-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

.search-area {
  display: flex;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
