<template>
  <MainLayout>
    <div class="announcement-detail-container">
      <el-card class="announcement-detail-card" v-loading="loading">
        <!-- 返回按钮 -->
        <div class="back-button">
          <el-button @click="goBack" :icon="ArrowLeft">返回列表</el-button>
        </div>
        
        <!-- 公告内容 -->
        <div v-if="announcement" class="announcement-content">
          <!-- 公告头部 -->
          <div class="announcement-header">
            <div class="announcement-meta">
              <el-tag v-if="announcement.isTop" type="danger" size="small">置顶</el-tag>
              <el-tag :type="getCategoryTagType(announcement.category)" size="small">
                {{ getCategoryName(announcement.category) }}
              </el-tag>
            </div>
            <h1 class="announcement-title">{{ announcement.title }}</h1>
            <div class="announcement-info">
              <div class="author-info">
                <el-avatar :size="32" :src="announcement.author?.avatar">
                  {{ getDisplayName(announcement.author)?.[0] || 'A' }}
                </el-avatar>
                <div class="author-details">
                  <span class="author-name">{{ getDisplayName(announcement.author) || '管理员' }}</span>
                  <span class="publish-time">{{ announcement.createTime }}</span>
                </div>
              </div>
              <div class="announcement-stats">
                <span class="stat-item">
                  <el-icon><View /></el-icon>
                  {{ announcement.viewCount }}
                </span>
                <span class="stat-item">
                  <el-icon><ChatDotRound /></el-icon>
                  {{ announcement.commentCount }}
                </span>
                <span class="stat-item">
                  <el-icon><Star /></el-icon>
                  {{ announcement.likeCount }}
                </span>
              </div>
            </div>
          </div>
          
          <!-- 公告正文 -->
          <div class="announcement-body">
            <div class="content-text" v-html="formatContent(announcement.content)"></div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="announcement-actions">
            <el-button 
              :type="announcement.liked ? 'primary' : 'default'"
              :icon="Star"
              @click="handleLike"
              :loading="liking"
            >
              {{ announcement.liked ? '已点赞' : '点赞' }} ({{ announcement.likeCount }})
            </el-button>
          </div>
        </div>
      </el-card>
      
      <!-- 评论区 -->
      <el-card class="comments-card">
        <template #header>
          <h3>评论 ({{ announcement?.commentCount || 0 }})</h3>
        </template>
        
        <!-- 发表评论 -->
        <div class="comment-form">
          <el-input
            v-model="newComment"
            type="textarea"
            :rows="3"
            placeholder="发表你的看法..."
            maxlength="500"
            show-word-limit
          />
          <div class="comment-actions">
            <el-button type="primary" @click="handleAddComment" :loading="commenting">
              发表评论
            </el-button>
          </div>
        </div>
        
        <!-- 评论列表 -->
        <div class="comments-list" v-loading="commentsLoading">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="comment-item"
          >
            <div class="comment-header">
              <div class="comment-user">
                <el-avatar :size="32" :src="comment.user?.avatar">
                  {{ getDisplayName(comment.user)?.[0] || 'U' }}
                </el-avatar>
                <div class="user-info">
                  <span class="username">{{ getDisplayName(comment.user) }}</span>
                  <span class="comment-time">{{ comment.createTime }}</span>
                </div>
              </div>
              <div class="comment-actions">
                <el-button
                  v-if="comment.user?.id === userStore.userInfo?.id"
                  type="danger"
                  size="small"
                  @click="handleDeleteComment(comment)"
                >
                  删除
                </el-button>
                <el-button
                  v-if="userStore.userInfo?.role === 'ADMIN' && comment.user?.id !== userStore.userInfo?.id"
                  type="danger"
                  size="small"
                  @click="handleAdminDeleteComment(comment)"
                >
                  删除
                </el-button>
              </div>
            </div>
            
            <div class="comment-content">{{ comment.content }}</div>
            
            <div class="comment-footer">
              <el-button
                :type="comment.liked ? 'primary' : 'default'"
                size="small"
                @click="handleCommentLike(comment)"
              >
                <el-icon><Star /></el-icon>
                {{ comment.likeCount }}
              </el-button>
              <el-button size="small" @click="showReplyForm(comment)">
                回复
              </el-button>
            </div>
            
            <!-- 回复表单 -->
            <div v-if="replyingTo === comment.id" class="reply-form">
              <el-input
                v-model="replyContent"
                type="textarea"
                :rows="2"
                :placeholder="`回复 ${getDisplayName(comment.user)}:`"
                maxlength="300"
                show-word-limit
              />
              <div class="reply-actions">
                <el-button size="small" @click="cancelReply">取消</el-button>
                <el-button type="primary" size="small" @click="handleReply(comment)" :loading="replying">
                  回复
                </el-button>
              </div>
            </div>
            
            <!-- 子评论 -->
            <div v-if="comment.replies && comment.replies.length > 0" class="replies">
              <div
                v-for="reply in comment.replies"
                :key="reply.id"
                class="reply-item"
              >
                <div class="reply-header">
                  <div class="reply-user">
                    <el-avatar :size="24" :src="reply.user?.avatar">
                      {{ getDisplayName(reply.user)?.[0] || 'U' }}
                    </el-avatar>
                    <span class="username">{{ getDisplayName(reply.user) }}</span>
                    <span class="reply-time">{{ reply.createTime }}</span>
                  </div>
                  <div class="reply-actions">
                    <el-button
                      v-if="reply.user?.id === userStore.userInfo?.id"
                      type="danger"
                      size="small"
                      @click="handleDeleteComment(reply)"
                    >
                      删除
                    </el-button>
                    <el-button
                      v-if="userStore.userInfo?.role === 'ADMIN' && reply.user?.id !== userStore.userInfo?.id"
                      type="danger"
                      size="small"
                      @click="handleAdminDeleteComment(reply)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
                <div class="reply-content">{{ reply.content }}</div>
                <div class="reply-footer">
                  <el-button
                    :type="reply.liked ? 'primary' : 'default'"
                    size="small"
                    @click="handleCommentLike(reply)"
                  >
                    <el-icon><Star /></el-icon>
                    {{ reply.likeCount }}
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <div v-if="comments.length === 0 && !commentsLoading" class="empty-comments">
            <el-empty description="暂无评论，快来发表第一条评论吧！" />
          </div>
        </div>
        
        <!-- 评论分页 -->
        <div class="comments-pagination">
          <el-pagination
            v-model:current-page="commentsPage"
            v-model:page-size="commentsPageSize"
            :page-sizes="[10, 20]"
            layout="total, sizes, prev, pager, next"
            :total="commentsTotal"
            @size-change="handleCommentsPageSizeChange"
            @current-change="handleCommentsPageChange"
          />
        </div>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, View, ChatDotRound, Star } from '@element-plus/icons-vue'
import MainLayout from '../components/MainLayout.vue'
import { useUserStore } from '../stores/user'
import { 
  getAnnouncementById, 
  toggleAnnouncementLike, 
  addAnnouncementComment,
  deleteAnnouncementComment,
  adminDeleteAnnouncementComment,
  toggleAnnouncementCommentLike,
  getAnnouncementComments
} from '../api'
import { getDisplayName } from '../utils/anonymize'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 数据
const loading = ref(false)
const announcement = ref(null)
const liking = ref(false)
const commenting = ref(false)
const replying = ref(false)
const commentsLoading = ref(false)

const newComment = ref('')
const replyContent = ref('')
const replyingTo = ref(null)

const comments = ref([])
const commentsTotal = ref(0)
const commentsPage = ref(1)
const commentsPageSize = ref(10)

// 获取公告详情
const fetchAnnouncement = async () => {
  try {
    loading.value = true
    const response = await getAnnouncementById(route.params.id)
    announcement.value = response.data
  } catch (error) {
    console.error('获取公告详情失败', error)
    ElMessage.error('获取公告详情失败')
  } finally {
    loading.value = false
  }
}

// 获取评论列表
const fetchComments = async () => {
  try {
    commentsLoading.value = true
    const response = await getAnnouncementComments(route.params.id, {
      pageNum: commentsPage.value,
      pageSize: commentsPageSize.value
    })
    comments.value = response.data.records
    commentsTotal.value = response.data.total
  } catch (error) {
    console.error('获取评论列表失败', error)
    ElMessage.error('获取评论列表失败')
  } finally {
    commentsLoading.value = false
  }
}

// 点赞
const handleLike = async () => {
  try {
    liking.value = true
    await toggleAnnouncementLike(announcement.value.id)
    announcement.value.liked = !announcement.value.liked
    announcement.value.likeCount += announcement.value.liked ? 1 : -1
    ElMessage.success(announcement.value.liked ? '点赞成功' : '取消点赞成功')
  } catch (error) {
    console.error('点赞失败', error)
    ElMessage.error('操作失败')
  } finally {
    liking.value = false
  }
}

// 发表评论
const handleAddComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }
  
  try {
    commenting.value = true
    await addAnnouncementComment(announcement.value.id, {
      content: newComment.value
    })
    newComment.value = ''
    ElMessage.success('评论发表成功')
    await fetchComments()
    await fetchAnnouncement() // 刷新评论数
  } catch (error) {
    console.error('发表评论失败', error)
    ElMessage.error('发表评论失败')
  } finally {
    commenting.value = false
  }
}

// 删除评论
const handleDeleteComment = async (comment) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteAnnouncementComment(comment.id)
    ElMessage.success('删除成功')
    await fetchComments()
    await fetchAnnouncement()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 管理员删除评论
const handleAdminDeleteComment = async (comment) => {
  try {
    await ElMessageBox.confirm('管理员操作: 确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await adminDeleteAnnouncementComment(comment.id)
    ElMessage.success('删除成功')
    await fetchComments()
    await fetchAnnouncement()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 评论点赞
const handleCommentLike = async (comment) => {
  try {
    await toggleAnnouncementCommentLike(comment.id)
    comment.liked = !comment.liked
    comment.likeCount += comment.liked ? 1 : -1
  } catch (error) {
    console.error('点赞失败', error)
    ElMessage.error('操作失败')
  }
}

// 显示回复表单
const showReplyForm = (comment) => {
  replyingTo.value = comment.id
  replyContent.value = ''
}

// 取消回复
const cancelReply = () => {
  replyingTo.value = null
  replyContent.value = ''
}

// 回复评论
const handleReply = async (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }
  
  try {
    replying.value = true
    await addAnnouncementComment(announcement.value.id, {
      content: replyContent.value,
      parentId: comment.id
    })
    cancelReply()
    ElMessage.success('回复成功')
    await fetchComments()
    await fetchAnnouncement()
  } catch (error) {
    console.error('回复失败', error)
    ElMessage.error('回复失败')
  } finally {
    replying.value = false
  }
}

// 评论分页
const handleCommentsPageSizeChange = (val) => {
  commentsPageSize.value = val
  fetchComments()
}

const handleCommentsPageChange = (val) => {
  commentsPage.value = val
  fetchComments()
}

// 工具函数
const goBack = () => {
  router.push('/announcement')
}

const getCategoryName = (category) => {
  const categoryMap = {
    NEWS: '热点新闻',
    ENTERTAINMENT: '娱乐周刊',
    LIVELIHOOD: '民生事件',
    POLICY: '惠民政策'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category) => {
  const typeMap = {
    NEWS: 'danger',
    ENTERTAINMENT: 'warning',
    LIVELIHOOD: 'info',
    POLICY: 'success'
  }
  return typeMap[category] || 'info'
}

const formatContent = (content) => {
  if (!content) return ''
  return content.replace(/\n/g, '<br>')
}

// 生命周期钩子
onMounted(() => {
  fetchAnnouncement()
  fetchComments()
})
</script>

<style scoped>
.announcement-detail-container {
  max-width: 1000px;
  margin: 0 auto;
}

.announcement-detail-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.back-button {
  margin-bottom: 20px;
}

.announcement-content {
  max-width: 100%;
}

.announcement-header {
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 20px;
  margin-bottom: 30px;
}

.announcement-meta {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.announcement-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.announcement-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-details {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 500;
  color: #303133;
}

.publish-time {
  font-size: 12px;
  color: #909399;
}

.announcement-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 14px;
}

.announcement-body {
  margin-bottom: 30px;
}

.content-text {
  font-size: 16px;
  line-height: 1.8;
  color: #303133;
}

.announcement-actions {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
}

.comments-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.comment-form {
  margin-bottom: 30px;
}

.comment-actions {
  margin-top: 10px;
  text-align: right;
}

.comments-list {
  min-height: 200px;
}

.comment-item {
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.comment-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.username {
  font-weight: 500;
  color: #303133;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-content {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #606266;
}

.comment-footer {
  display: flex;
  gap: 12px;
}

.reply-form {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.reply-actions {
  margin-top: 10px;
  text-align: right;
}

.replies {
  margin-top: 16px;
  padding-left: 40px;
}

.reply-item {
  border-top: 1px solid #f0f0f0;
  padding: 16px 0;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reply-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reply-time {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

.reply-content {
  margin-bottom: 8px;
  line-height: 1.6;
  color: #606266;
}

.reply-footer {
  display: flex;
  gap: 12px;
}

.empty-comments {
  text-align: center;
  padding: 40px 0;
}

.comments-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
