<template>
  <MainLayout>
    <div class="k-anonymity-container">
      <el-card class="config-card">
        <template #header>
          <div class="card-header">
            <h2 class="card-title">K匿名配置管理</h2>
            <el-button type="primary" @click="showCreateDialog">
              新建配置
            </el-button>
          </div>
        </template>

        <el-table :data="configs" style="width: 100%">
          <el-table-column prop="kvalue" label="K值" width="100"/>
          <el-table-column prop="strategy" label="匿名化策略" width="150"/>
          <el-table-column prop="description" label="描述"/>
          <el-table-column prop="enabled" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'info'">
                {{ row.enabled ? '已启用' : '已禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                  v-if="!row.enabled"
                  type="success"
                  size="small"
                  @click="handleToggle(row)"
              >
                启用
              </el-button>
              <el-button
                  v-else
                  type="info"
                  size="small"
                  @click="handleToggle(row)"
              >
                禁用
              </el-button>
              <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '编辑配置' : '新建配置'"
        width="500px"
    >
      <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
      >
<!--        <el-form-item label="K值" prop="kvalue">-->
<!--          <el-input-number-->
<!--              v-model="form.kvalue"-->
<!--              :min="2"-->
<!--              :max="100"-->
<!--              controls-position="right"-->
<!--          />-->
<!--        </el-form-item>-->
        <el-form-item label="匿名化策略" prop="strategy">
          <el-select v-model="form.strategy" placeholder="请选择匿名化策略">
            <el-option label="基础策略" value="BASIC"/>
            <el-option label="增强策略" value="ENHANCED"/>
            <el-option label="严格策略" value="STRICT"/>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入配置描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-switch v-model="form.enabled"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </MainLayout>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '../stores/user'
import {ElMessage, ElMessageBox} from 'element-plus'
import MainLayout from '../components/MainLayout.vue'
import {
  getKAnonymityConfigs,
  createKAnonymityConfig,
  updateKAnonymityConfig,
  toggleKAnonymityConfig,
  deleteKAnonymityConfig
} from '../api'

const router = useRouter()
const userStore = useUserStore()
const configs = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref(null)

const form = ref({
  kvalue: 2,
  strategy: 'BASIC',
  description: '',
  enabled: true
})

const rules = {
  // kvalue: [
  //   {required: true, message: '请输入K值', trigger: 'blur'},
  //   {type: 'number', min: 2, message: 'K值必须大于等于2', trigger: 'blur'}
  // ],
  strategy: [
    {required: true, message: '请选择匿名化策略', trigger: 'change'}
  ],
  description: [
    {required: true, message: '请输入配置描述', trigger: 'blur'}
  ]
}

const fetchConfigs = async () => {
  try {
    const response = await getKAnonymityConfigs()
    configs.value = response.data
  } catch (error) {
    ElMessage.error('获取配置列表失败')
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  form.value = {
    kvalue: 2,
    strategy: 'BASIC',
    description: '',
    enabled: true
  }
  dialogVisible.value = true
}

const handleEdit = (row) => {
  isEdit.value = true
  form.value = {...row}
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (isEdit.value) {
      await updateKAnonymityConfig(form.value)
      ElMessage.success('更新成功')
    } else {
      await createKAnonymityConfig(form.value)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchConfigs()
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '操作失败')
  }
}

const handleToggle = async (row) => {
  try {
    await toggleKAnonymityConfig(row.id, !row.enabled)
    ElMessage.success(row.enabled ? '已禁用' : '已启用')
    fetchConfigs()
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '操作失败')
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
        '确定要删除该配置吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    await deleteKAnonymityConfig(row.id)
    ElMessage.success('删除成功')
    fetchConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}


onMounted(() => {
  fetchConfigs()
})
</script>

<style scoped>
.k-anonymity-container {
  max-width: 1200px;
  margin: 0 auto;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 5px;
}

@media screen and (max-width: 768px) {
  .content-wrapper {
    padding: 0 10px;
  }
}
</style>