<template>
  <div class="page-container register-container">
    <div class="form-container">
      <el-card class="register-card">
        <h2 class="form-title">注册账号</h2>
        <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="rules"
            label-position="top"
            @submit.prevent="handleRegister"
        >
          <el-form-item label="用户名" prop="username">
            <el-input
                v-model="registerForm.username"
                placeholder="请输入用户名"
                :prefix-icon="User"
            />
          </el-form-item>

          <el-form-item label="昵称" prop="nickname">
            <el-input
                v-model="registerForm.nickname"
                placeholder="请输入昵称"
                :prefix-icon="UserFilled"
            />
          </el-form-item>

          <el-form-item label="真实姓名" prop="realName">
            <el-input
                v-model="registerForm.realName"
                placeholder="请输入真实姓名"
                :prefix-icon="UserFilled"
            />
          </el-form-item>

          <el-form-item label="手机号码" prop="phone">
            <el-input
                v-model="registerForm.phone"
                placeholder="请输入手机号码"
                :prefix-icon="Iphone"
            />
          </el-form-item>

          <el-form-item label="家庭住址" prop="address">
            <el-input
                v-model="registerForm.address"
                placeholder="请输入家庭住址"
                :prefix-icon="Location"
            />
          </el-form-item>

          <el-form-item label="密码" prop="password">
            <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="请再次输入密码"
                :prefix-icon="Lock"
                show-password
            />
          </el-form-item>

          <div class="form-footer">
            <el-button
                type="primary"
                :loading="loading"
                class="submit-btn"
                @click="handleRegister"
            >
              注册并登录
            </el-button>
            <div class="form-links">
              <el-link type="primary" @click="$router.push('/login')">
                已有账号？去登录
              </el-link>
            </div>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '../stores/user'
import {ElMessage} from 'element-plus'
import {User, UserFilled, Lock, Iphone, Location} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()
const registerFormRef = ref(null)

const registerForm = ref({
  username: '',
  nickname: '',
  realName: '',
  phone: '',
  address: '',
  password: '',
  confirmPassword: ''
})

const loading = ref(false)

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (registerForm.value.confirmPassword !== '') {
      registerFormRef.value.validateField('confirmPassword')
    }
    callback()
  }
}

const validateConfirmPass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.value.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback()
    return
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

const rules = {
  username: [
    {required: true, message: '请输入用户名', trigger: 'blur'},
    {min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur'}
  ],
  nickname: [
    {required: true, message: '请输入昵称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  realName: [
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  phone: [
    {validator: validatePhone, trigger: 'blur'}
  ],
  address: [
    {max: 100, message: '长度不能超过 100 个字符', trigger: 'blur'}
  ],
  password: [
    {validator: validatePass, trigger: 'blur'},
    {min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur'}
  ],
  confirmPassword: [
    {validator: validateConfirmPass, trigger: 'blur'}
  ]
}

const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    await registerFormRef.value.validate()
    loading.value = true

    const {confirmPassword, ...userData} = registerForm.value
    await userStore.handleRegister(userData)

    ElMessage.success('注册成功')
    router.push('/')
  } catch (error) {
    ElMessage.error(error.response?.data?.message || '注册失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-color) 0%, #1989fa 100%);
}

.register-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.form-footer {
  margin-top: 30px;
}

.submit-btn {
  width: 100%;
  padding: 12px 20px;
  font-size: 16px;
}

.form-links {
  margin-top: 15px;
  text-align: center;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  padding: 8px 15px;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, #1989fa 100%);
  border: none;
}
</style>