<template>
  <MainLayout>
    <div class="profile-container">
      <el-card class="profile-card">
        <template #header>
          <h2 class="card-title">个人资料</h2>
        </template>
        <el-form
            ref="profileForm"
            :model="profileData"
            :rules="rules"
            label-width="100px"
            @submit.prevent="handleUpdateProfile"
        >
          <el-form-item label="头像">
            <el-upload
                class="avatar-uploader"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                :headers="uploadHeaders"
            >
              <img v-if="profileData.avatar" :src="profileData.avatar" class="avatar"/>
              <el-icon v-else class="avatar-uploader-icon">
                <Plus/>
              </el-icon>
            </el-upload>
          </el-form-item>

          <el-form-item label="用户名">
            <el-input v-model="profileData.username" disabled/>
          </el-form-item>

          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="profileData.nickname"/>
          </el-form-item>

          <el-form-item label="真实姓名" prop="realName">
            <el-input v-model="profileData.realName" placeholder="请输入真实姓名"/>
          </el-form-item>

          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="profileData.phone" placeholder="请输入手机号码"/>
          </el-form-item>

          <el-form-item label="家庭住址" prop="address">
            <el-input v-model="profileData.address" placeholder="请输入家庭住址"/>
          </el-form-item>

          <el-form-item label="旧密码" prop="oldPassword">
            <el-input
                v-model="profileData.oldPassword"
                type="password"
                show-password
                placeholder="不修改请留空"
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
                v-model="profileData.newPassword"
                type="password"
                show-password
                placeholder="不修改请留空"
            />
          </el-form-item>

          <el-form-item label="确认新密码" prop="confirmPassword">
            <el-input
                v-model="profileData.confirmPassword"
                type="password"
                show-password
                placeholder="不修改请留空"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" native-type="submit" :loading="updating">
              保存修改
            </el-button>
            <el-button @click="$router.push('/')">返回首页</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 隐私设置卡片 -->
      <el-card class="profile-card">
        <template #header>
          <h2 class="card-title">隐私设置</h2>
        </template>

        <el-form :model="privacySettings" label-width="120px">
          <el-form-item label="个人资料可见性">
            <el-select v-model="privacySettings.profileVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
            <div class="privacy-hint">控制昵称、头像等基本资料的可见性</div>
          </el-form-item>

          <el-form-item label="真实姓名可见性">
            <el-select v-model="privacySettings.realNameVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>

          <el-form-item label="手机号码可见性">
            <el-select v-model="privacySettings.phoneVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>

          <el-form-item label="家庭住址可见性">
            <el-select v-model="privacySettings.addressVisibility" class="privacy-select">
              <el-option label="公开" value="PUBLIC" />
              <el-option label="仅好友可见" value="FRIENDS_ONLY" />
              <el-option label="私密" value="PRIVATE" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" :loading="savingPrivacy" @click="savePrivacySettings">保存隐私设置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </MainLayout>
</template>

<script setup>
import {ref, onMounted, nextTick} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '../stores/user'
import {ElMessage} from 'element-plus'
import MainLayout from '../components/MainLayout.vue'
import {
  Plus
} from '@element-plus/icons-vue'
import {getUserInfo, updateUserInfo, getPrivacySettings, updatePrivacySettings} from '../api'
import { getDisplayName, anonymizeUserInfo } from '../utils/anonymize'

const router = useRouter()
const userStore = useUserStore()
const profileForm = ref(null)
const profileData = ref({
  username: '',
  nickname: '',
  avatar: '',
  realName: '',
  phone: '',
  address: '',
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const updating = ref(false)
const savingPrivacy = ref(false)

// 隐私设置数据模型
const privacySettings = ref({
  profileVisibility: 'PUBLIC',
  realNameVisibility: 'FRIENDS_ONLY',
  phoneVisibility: 'PRIVATE',
  addressVisibility: 'PRIVATE',
  postsVisibility: 'PUBLIC',
  friendsListVisibility: 'PUBLIC',
  onlineStatusVisibility: 'PUBLIC'
})

const fetchProfile = async () => {
  try {
    // 使用当前用户的ID获取用户信息
    const currentUserId = userStore.userInfo?.id
    if (!currentUserId) {
      ElMessage.error('未获取到用户ID')
      return
    }

    const response = await getUserInfo(currentUserId)
    const {password, ...userInfo} = response.data
    Object.assign(profileData.value, userInfo)
    await nextTick()
    if (profileForm.value) {
      profileForm.value.clearValidate()
    }
  } catch (error) {
    ElMessage.error('获取用户信息失败')
    console.error(error)
  }
}

onMounted(() => {
  fetchProfile()
  fetchPrivacySettings()
})

const validatePass = (rule, value, callback) => {
  if (profileData.value.newPassword && !value) {
    callback(new Error('修改密码时必须输入旧密码'))
  } else {
    callback()
  }
}

const validateNewPass = (rule, value, callback) => {
  if (value && !profileData.value.oldPassword) {
    callback(new Error('请先输入旧密码'))
  } else {
    if (profileData.value.confirmPassword !== '') {
      profileForm.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validateConfirmPass = (rule, value, callback) => {
  if (profileData.value.newPassword && !value) {
    callback(new Error('请确认新密码'))
  } else if (value !== profileData.value.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

// 手机号码验证函数
const validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback()
    return
  }
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号码'))
  } else {
    callback()
  }
}

const rules = {
  nickname: [
    {required: true, message: '请输入昵称', trigger: 'blur'},
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  realName: [
    {min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur'}
  ],
  phone: [
    {validator: validatePhone, trigger: 'blur'}
  ],
  address: [
    {max: 100, message: '长度不能超过 100 个字符', trigger: 'blur'}
  ],
  oldPassword: [
    {validator: validatePass, trigger: 'blur'}
  ],
  newPassword: [
    {validator: validateNewPass, trigger: 'blur'},
    {min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur'}
  ],
  confirmPassword: [
    {validator: validateConfirmPass, trigger: 'blur'}
  ]
}

const handleUpdateProfile = async () => {
  if (!profileForm.value) return

  try {
    await profileForm.value.validate()
    updating.value = true

    const updateData = {
      nickname: profileData.value.nickname,
      avatar: profileData.value.avatar,
      realName: profileData.value.realName,
      phone: profileData.value.phone,
      address: profileData.value.address
    }

    // 如果输入了新密码，则添加密码相关字段
    if (profileData.value.newPassword) {
      updateData.oldPassword = profileData.value.oldPassword
      updateData.password = profileData.value.newPassword
    }

    await updateUserInfo(updateData)
    ElMessage.success('更新成功')
    await userStore.fetchUserInfo()

    // 清空密码字段
    profileData.value.oldPassword = ''
    profileData.value.newPassword = ''
    profileData.value.confirmPassword = ''
  } catch (error) {
    if (error.response?.data?.message) {
      ElMessage.error(error.response.data.message)
    } else {
      ElMessage.error('更新失败，请稍后重试')
    }
  } finally {
    updating.value = false
  }
}

const uploadHeaders = {
  Authorization: `Bearer ${localStorage.getItem('token')}`
}

// 获取隐私设置
const fetchPrivacySettings = async () => {
  try {
    const response = await getPrivacySettings()
    Object.assign(privacySettings.value, response.data)
  } catch (error) {
    ElMessage.error('获取隐私设置失败')
    console.error(error)
  }
}

// 保存隐私设置
const savePrivacySettings = async () => {
  try {
    savingPrivacy.value = true
    await updatePrivacySettings(privacySettings.value)
    ElMessage.success('隐私设置已更新')
  } catch (error) {
    ElMessage.error('保存隐私设置失败')
    console.error(error)
  } finally {
    savingPrivacy.value = false
  }
}

const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    profileData.value.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('上传头像图片只能是图片格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
  }

  return isImage && isLt2M
}

const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'friends':
      router.push('/friends')
      break
    case 'dashboard':
      router.push('/admin/dashboard')
      break
    case 'k-anonymity':
      router.push('/admin/k-anonymity')
      break
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

</script>

<style scoped>
.profile-container {
  max-width: 800px;
  margin: 0 auto;
}

.avatar {
  height: 200px;
  width: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.profile-card {
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.privacy-select {
  width: 100%;
  max-width: 300px;
}

.privacy-hint {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

:deep(.el-card__header) {
  background-color: #f8f9fa;
  padding: 15px 20px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
}

:deep(.avatar-uploader .el-upload) {
  border: 1px dashed var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

:deep(.avatar-uploader .el-upload:hover) {
  border-color: var(--primary-color);
}

:deep(.avatar-uploader-icon) {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 200px;
  line-height: 200px;
  text-align: center;
}

@media screen and (max-width: 768px) {
  .profile-container {
    padding: 0 10px;
  }
}
</style>