/**
 * 匿名化工具函数
 */

/**
 * 匿名化真实姓名 - 中间字符用*替换
 * @param {string} realName - 真实姓名
 * @returns {string} 匿名化后的姓名
 */
export function anonymizeRealName(realName) {
  if (!realName || realName.length === 0) {
    return '匿名用户'
  }
  
  if (realName.length === 1) {
    return realName
  }
  
  if (realName.length === 2) {
    return realName[0] + '*'
  }
  
  // 3个字符及以上：保留首尾，中间用*替换
  const firstChar = realName[0]
  const lastChar = realName[realName.length - 1]
  const middleStars = '*'.repeat(realName.length - 2)
  
  return firstChar + middleStars + lastChar
}

/**
 * 匿名化手机号 - 保留前3位和后4位
 * @param {string} phone - 手机号
 * @returns {string} 匿名化后的手机号
 */
export function anonymizePhone(phone) {
  if (!phone || phone.length < 7) {
    return phone
  }
  
  if (phone.length === 11) {
    // 标准手机号：156****3333
    return phone.substring(0, 3) + '****' + phone.substring(7)
  }
  
  // 其他长度的号码
  const visibleLength = Math.min(3, Math.floor(phone.length / 3))
  const prefix = phone.substring(0, visibleLength)
  const suffix = phone.substring(phone.length - visibleLength)
  const stars = '*'.repeat(phone.length - visibleLength * 2)
  
  return prefix + stars + suffix
}

/**
 * 匿名化地址 - 保留省市，详细地址部分匿名
 * @param {string} address - 地址
 * @returns {string} 匿名化后的地址
 */
export function anonymizeAddress(address) {
  if (!address || address.length === 0) {
    return address
  }
  
  // 如果地址很短，只显示前几个字符
  if (address.length <= 6) {
    return address.substring(0, 2) + '*'.repeat(address.length - 2)
  }
  
  // 尝试识别省市区模式
  const provincePattern = /(.*?省|.*?市|.*?自治区|.*?特别行政区)/
  const cityPattern = /(.*?市|.*?县|.*?区)/
  
  let result = address
  
  // 保留省份信息
  const provinceMatch = address.match(provincePattern)
  if (provinceMatch) {
    const province = provinceMatch[1]
    const remaining = address.substring(province.length)
    
    // 保留市区信息
    const cityMatch = remaining.match(cityPattern)
    if (cityMatch) {
      const city = cityMatch[1]
      const detailAddress = remaining.substring(city.length)
      
      // 详细地址匿名化
      if (detailAddress.length > 4) {
        const anonymizedDetail = detailAddress.substring(0, 2) + '*'.repeat(detailAddress.length - 4) + detailAddress.substring(detailAddress.length - 2)
        result = province + city + anonymizedDetail
      } else {
        result = province + city + '*'.repeat(detailAddress.length)
      }
    } else {
      // 没有识别到市区，保留省份，其余匿名
      const remaining = address.substring(province.length)
      if (remaining.length > 4) {
        result = province + remaining.substring(0, 2) + '*'.repeat(remaining.length - 4) + remaining.substring(remaining.length - 2)
      } else {
        result = province + '*'.repeat(remaining.length)
      }
    }
  } else {
    // 没有识别到省份，简单匿名化
    if (address.length > 6) {
      result = address.substring(0, 3) + '*'.repeat(address.length - 6) + address.substring(address.length - 3)
    } else {
      result = address.substring(0, 2) + '*'.repeat(address.length - 2)
    }
  }
  
  return result
}

/**
 * 获取显示名称 - 优先使用匿名化的真实姓名，其次使用昵称
 * @param {Object} user - 用户对象
 * @returns {string} 显示名称
 */
export function getDisplayName(user) {
  if (!user) {
    return '匿名用户'
  }
  
  // 优先使用真实姓名（匿名化）
  if (user.realName) {
    return anonymizeRealName(user.realName)
  }
  
  // 其次使用昵称
  if (user.nickname) {
    return user.nickname
  }
  
  // 最后使用用户名
  if (user.username) {
    return user.username
  }
  
  return '匿名用户'
}

/**
 * 匿名化用户信息 - 对用户对象进行深度匿名化
 * @param {Object} user - 用户对象
 * @returns {Object} 匿名化后的用户对象
 */
export function anonymizeUserInfo(user) {
  if (!user) {
    return user
  }
  
  const anonymizedUser = { ...user }
  
  // 匿名化手机号
  if (anonymizedUser.phone) {
    anonymizedUser.phone = anonymizePhone(anonymizedUser.phone)
  }
  
  // 匿名化地址
  if (anonymizedUser.address) {
    anonymizedUser.address = anonymizeAddress(anonymizedUser.address)
  }
  
  return anonymizedUser
}
