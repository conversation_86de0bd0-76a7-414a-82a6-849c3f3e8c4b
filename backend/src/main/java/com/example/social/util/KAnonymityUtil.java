package com.example.social.util;

import com.example.social.dto.CommentDTO;
import com.example.social.dto.PostDTO;
import com.example.social.dto.UserDTO;
import com.example.social.entity.KAnonymityConfig;

import java.util.*;
import java.util.stream.Collectors;

public class KAnonymityUtil {
    
    /**
     * 对用户信息进行匿名化处理
     */
    public static void anonymizeUserInfo(UserDTO user, KAnonymityConfig config) {
        if (user == null || config == null || !config.getEnabled()) {
            return;
        }

        switch (config.getStrategy()) {
            case "BASIC":
                // 基础策略：隐藏用户名和昵称的部分字符
                user.setUsername(maskUsername(user.getUsername()));
                user.setNickname(maskNickname(user.getNickname()));
                break;
            case "ENHANCED":
                // 增强策略：用户名和昵称显示更少的字符，头像模糊化
                user.setUsername(strongMaskUsername(user.getUsername()));
                user.setNickname(strongMaskNickname(user.getNickname()));
                user.setAvatar("/default-avatar.png");
                break;  
            case "STRICT":
                // 严格策略：完全隐藏用户信息
                user.setUsername("匿名用户");
                user.setNickname("匿名用户");
                user.setAvatar("/default-avatar.png");
                break;
        }
    }

    /**
     * 对用户列表进行K匿名化处理
     */
    public static void anonymizeUserList(List<UserDTO> users, KAnonymityConfig config) {
        if (users == null || users.isEmpty() || config == null || !config.getEnabled()) {
            return;
        }

        // 如果用户数量小于K值，对所有用户进行匿名化处理
        if (users.size() < config.getKValue()) {
            users.forEach(user -> anonymizeUserInfo(user, config));
            return;
        }

        // 按照K值分组进行处理
        for (int i = 0; i < users.size(); i += config.getKValue()) {
            int end = Math.min(i + config.getKValue(), users.size());
            List<UserDTO> group = users.subList(i, end);
            group.forEach(user -> anonymizeUserInfo(user, config));
        }
    }

    /**
     * 对帖子中的用户信息进行匿名化处理
     */
    public static void anonymizePostInfo(PostDTO post, KAnonymityConfig config) {
        if (post == null || config == null || !config.getEnabled()) {
            return;
        }

        // 处理帖子作者信息
        if (post.getUser() != null) {
            anonymizeUserInfo(post.getUser(), config);
        }

        // 处理评论用户信息
//        if (post.getComments() != null) {
//            post.getComments().forEach(comment -> {
//                if (comment.getUser() != null) {
//                    anonymizeUserInfo(comment.getUser(), config);
//                }
//            });
//        }
    }

    /**
     * 对帖子列表中的用户信息进行匿名化处理
     */
    public static void anonymizePostList(List<PostDTO> posts, KAnonymityConfig config) {
        if (posts == null || posts.isEmpty() || config == null || !config.getEnabled()) {
            return;
        }

        posts.forEach(post -> anonymizePostInfo(post, config));
    }

    // 基础掩码：保留首尾字符
    private static String maskUsername(String username) {
        if (username == null || username.length() <= 2) {
            return username;
        }
        return username.charAt(0) + repeatChar('*', username.length() - 2) + username.charAt(username.length() - 1);
    }

    private static String maskNickname(String nickname) {
        if (nickname == null || nickname.length() <= 2) {
            return nickname;
        }
        return nickname.charAt(0) + repeatChar('*', nickname.length() - 2) + nickname.charAt(nickname.length() - 1);
    }

    // 增强掩码：只保留首字符
    private static String strongMaskUsername(String username) {
        if (username == null || username.isEmpty()) {
            return username;
        }
        return username.charAt(0) + repeatChar('*', Math.max(2, username.length() - 1));
    }

    private static String strongMaskNickname(String nickname) {
        if (nickname == null || nickname.isEmpty()) {
            return nickname;
        }
        return nickname.charAt(0) + repeatChar('*', Math.max(2, nickname.length() - 1));
    }

    /**
     * 重复字符串指定次数
     */
    private static String repeatChar(char ch, int count) {
        StringBuilder sb = new StringBuilder(count);
        for (int i = 0; i < count; i++) {
            sb.append(ch);
        }
        return sb.toString();
    }
} 