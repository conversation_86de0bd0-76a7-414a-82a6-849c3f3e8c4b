package com.example.social.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.entity.PostLike;

public interface PostLikeService extends IService<PostLike> {
    
    /**
     * 检查用户是否点赞了指定动态
     */
    boolean hasLiked(Long postId, Long userId);
    
    /**
     * 切换点赞状态，返回是否点赞
     * @return true 表示点赞，false 表示取消点赞
     */
    boolean toggleLike(Long postId, Long userId);
} 