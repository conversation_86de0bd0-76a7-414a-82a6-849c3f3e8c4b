package com.example.social.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.social.dto.SensitiveWordDTO;
import com.example.social.entity.SensitiveWord;

import java.util.List;

/**
 * 敏感词服务接口
 */
public interface SensitiveWordService {
    
    /**
     * 分页查询敏感词
     */
    Page<SensitiveWordDTO> page(int pageNum, int pageSize, String keyword, String category);
    
    /**
     * 添加敏感词
     */
    void add(SensitiveWordDTO dto);
    
    /**
     * 更新敏感词
     */
    void update(SensitiveWordDTO dto);
    
    /**
     * 删除敏感词
     */
    void delete(Long id);
    
    /**
     * 批量导入敏感词
     */
    int batchImport(List<String> words, String category, Integer level);
    
    /**
     * 过滤文本中的敏感词
     * @param text 原文本
     * @return 过滤后的文本
     */
    String filter(String text);
    
    /**
     * 检查文本是否包含敏感词
     * @param text 文本
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWords(String text);
    
    /**
     * 获取所有敏感词
     */
    List<SensitiveWord> getAllSensitiveWords();
}
