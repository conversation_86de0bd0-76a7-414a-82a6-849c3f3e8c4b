package com.example.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.social.common.BusinessException;
import com.example.social.common.JwtUtil;
import com.example.social.dto.LoginRequest;
import com.example.social.dto.RegisterRequest;
import com.example.social.dto.UserDTO;
import com.example.social.entity.User;
import com.example.social.mapper.UserMapper;
import com.example.social.security.UserDetailsImpl;
import com.example.social.service.UserPrivacySettingsService;
import com.example.social.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.example.social.event.UserInfoRequestEvent;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDTO register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (lambdaQuery().eq(User::getUsername, request.getUsername()).exists()) {
            throw new BusinessException("用户名已存在");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setRealName(request.getRealName());
        user.setPhone(request.getPhone());
        user.setAddress(request.getAddress());

        // 保存用户
        save(user);

        return convertToDTO(user);
    }

    @Override
    public String login(LoginRequest request) {
        // 获取用户信息
        User user = getByUsername(request.getUsername());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 验证密码
        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            throw new BusinessException("密码错误");
        }

        // 生成JWT令牌
        UserDetailsImpl userDetails = new UserDetailsImpl(user);
        return jwtUtil.generateToken(userDetails);
    }

    @Override
    public UserDTO getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        return convertToDTO(userDetails.getUser());
    }

    @Override
    public User getByUsername(String username) {
        return lambdaQuery()
            .eq(User::getUsername, username)
            .one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserDTO updateUser(User user) {
        if (user.getId() == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 获取当前用户
        User currentUser = getById(user.getId());
        if (currentUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 如果要更新密码，需要验证旧密码
        if (user.getPassword() != null && !user.getPassword().isEmpty()) {
            String oldPassword = user.getOldPassword();
            if (oldPassword == null || oldPassword.isEmpty()) {
                throw new BusinessException("请输入旧密码");
            }

            if (!passwordEncoder.matches(oldPassword, currentUser.getPassword())) {
                throw new BusinessException("旧密码错误");
            }

            // 加密新密码
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        // 更新用户信息
        updateById(user);

        return convertToDTO(getById(user.getId()));
    }

    @Override
    public UserDTO convertToDTO(User user) {
        if (user == null) {
            return null;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);
        return userDTO;
    }

    @Override
    public UserDTO getUserInfo(Long userId) {
        // 获取当前用户ID
        Long currentUserId = null;
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
                UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
                currentUserId = userDetails.getUser().getId();
            }
        } catch (Exception ignored) {
            // 未登录用户，currentUserId保持为null
        }

        // 获取目标用户信息
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 转换为DTO
        UserDTO userDTO = convertToDTO(user);

        // 应用后端匿名化处理
        applyAnonymization(userDTO);

        // 发布用户信息请求事件，由UserPrivacySettingsService处理
        eventPublisher.publishEvent(new UserInfoRequestEvent(this, userId, currentUserId, userDTO));

        return userDTO;
    }

    @Override
    public List<UserDTO> searchByNickname(String nickname) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<User>()
                .like(User::getNickname, nickname)
                .or()
                .like(User::getUsername, nickname);
        return list(wrapper).stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 应用匿名化处理
     */
    private void applyAnonymization(UserDTO userDTO) {
        // 匿名化手机号
        if (userDTO.getPhone() != null && !userDTO.getPhone().isEmpty()) {
            userDTO.setPhone(anonymizePhone(userDTO.getPhone()));
        }

        // 匿名化地址
        if (userDTO.getAddress() != null && !userDTO.getAddress().isEmpty()) {
            userDTO.setAddress(anonymizeAddress(userDTO.getAddress()));
        }
    }

    /**
     * 匿名化手机号
     */
    private String anonymizePhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return phone;
        }

        if (phone.length() == 11) {
            // 标准手机号：156****3333
            return phone.substring(0, 3) + "****" + phone.substring(7);
        }

        // 其他长度的号码
        int visibleLength = Math.min(3, phone.length() / 3);
        String prefix = phone.substring(0, visibleLength);
        String suffix = phone.substring(phone.length() - visibleLength);
        String stars = "*".repeat(phone.length() - visibleLength * 2);

        return prefix + stars + suffix;
    }

    /**
     * 匿名化地址
     */
    private String anonymizeAddress(String address) {
        if (address == null || address.length() == 0) {
            return address;
        }

        // 如果地址很短，只显示前几个字符
        if (address.length() <= 6) {
            return address.substring(0, 2) + "*".repeat(address.length() - 2);
        }

        // 简单匿名化：保留前3位和后3位
        if (address.length() > 6) {
            return address.substring(0, 3) + "*".repeat(address.length() - 6) + address.substring(address.length() - 3);
        } else {
            return address.substring(0, 2) + "*".repeat(address.length() - 2);
        }
    }
}