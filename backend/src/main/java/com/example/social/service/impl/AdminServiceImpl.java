package com.example.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.example.social.dto.DailyStatsDTO;
import com.example.social.dto.DashboardStatsDTO;
import com.example.social.entity.Comment;
import com.example.social.entity.Post;
import com.example.social.entity.PostLike;
import com.example.social.entity.User;
import com.example.social.mapper.CommentMapper;
import com.example.social.mapper.PostLikeMapper;
import com.example.social.mapper.PostMapper;
import com.example.social.mapper.UserMapper;
import com.example.social.service.AdminService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AdminServiceImpl implements AdminService {

    private final UserMapper userMapper;
    private final PostMapper postMapper;
    private final CommentMapper commentMapper;
    private final PostLikeMapper postLikeMapper;

    @Override
    public DashboardStatsDTO getDashboardStats() {
        DashboardStatsDTO stats = new DashboardStatsDTO();

        // 获取总数据统计
        stats.setTotalUsers(Math.toIntExact(userMapper.selectCount(null)));
        stats.setTotalPosts(Math.toIntExact(postMapper.selectCount(null)));
        stats.setTotalComments(Math.toIntExact(commentMapper.selectCount(null)));
        stats.setTotalLikes(Math.toIntExact(postLikeMapper.selectCount(null)));

        // 获取今日数据统计
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

        LambdaQueryWrapper<User> userQuery = new LambdaQueryWrapper<>();
//        userQuery.between(User::getLastLoginTime, todayStart, todayEnd);
        stats.setDailyActiveUsers(Math.toIntExact(userMapper.selectCount(userQuery)));

        LambdaQueryWrapper<Post> postQuery = new LambdaQueryWrapper<>();
        postQuery.between(Post::getCreateTime, todayStart, todayEnd);
        stats.setDailyPosts(Math.toIntExact(postMapper.selectCount(postQuery)));

        LambdaQueryWrapper<Comment> commentQuery = new LambdaQueryWrapper<>();
        commentQuery.between(Comment::getCreateTime, todayStart, todayEnd);
        stats.setDailyComments(Math.toIntExact(commentMapper.selectCount(commentQuery)));

        LambdaQueryWrapper<PostLike> likeQuery = new LambdaQueryWrapper<>();
        likeQuery.between(PostLike::getCreateTime, todayStart, todayEnd);
        stats.setDailyLikes(Math.toIntExact(postLikeMapper.selectCount(likeQuery)));

        // 获取7日数据统计
        List<DailyStatsDTO> weeklyStats = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd");

        for (int i = 6; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            LocalDateTime dayStart = LocalDateTime.of(date, LocalTime.MIN);
            LocalDateTime dayEnd = LocalDateTime.of(date, LocalTime.MAX);

            DailyStatsDTO dailyStats = new DailyStatsDTO();
            dailyStats.setDate(date.format(formatter));

            // 获取当日活跃用户数
            userQuery.clear();
//            userQuery.between(User::getLastLoginTime, dayStart, dayEnd);
            dailyStats.setActiveUsers(Math.toIntExact(userMapper.selectCount(userQuery)));

            // 获取当日发布动态数
            postQuery.clear();
            postQuery.between(Post::getCreateTime, dayStart, dayEnd);
            dailyStats.setPosts(Math.toIntExact(postMapper.selectCount(postQuery)));

            // 获取当日评论数
            commentQuery.clear();
            commentQuery.between(Comment::getCreateTime, dayStart, dayEnd);
            dailyStats.setComments(Math.toIntExact(commentMapper.selectCount(commentQuery)));

            // 获取当日点赞数
            likeQuery.clear();
            likeQuery.between(PostLike::getCreateTime, dayStart, dayEnd);
            dailyStats.setLikes(Math.toIntExact(postLikeMapper.selectCount(likeQuery)));

            weeklyStats.add(dailyStats);
        }

        stats.setWeeklyStats(weeklyStats);
        return stats;
    }
} 