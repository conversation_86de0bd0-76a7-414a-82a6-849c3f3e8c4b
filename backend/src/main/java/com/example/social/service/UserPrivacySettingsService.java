package com.example.social.service;

import com.example.social.dto.PrivacySettingsDTO;
import com.example.social.dto.UpdatePrivacySettingsRequest;
import com.example.social.entity.UserPrivacySettings;
import com.example.social.enums.PrivacyLevel;

/**
 * 用户隐私设置服务接口
 */
public interface UserPrivacySettingsService {
    
    /**
     * 获取当前用户的隐私设置
     */
    PrivacySettingsDTO getCurrentUserPrivacySettings();
    
    /**
     * 更新当前用户的隐私设置
     */
    PrivacySettingsDTO updatePrivacySettings(UpdatePrivacySettingsRequest request);
    
    /**
     * 获取用户的隐私设置
     */
    UserPrivacySettings getByUserId(Long userId);
    
    /**
     * 创建默认隐私设置
     */
    UserPrivacySettings createDefaultSettings(Long userId);
    
    /**
     * 检查是否有权限查看特定字段
     */
    boolean canViewField(Long targetUserId, String fieldName, Long currentUserId);
    
    /**
     * 根据隐私设置过滤用户DTO
     */
    void applyPrivacyFilter(Long targetUserId, Object dto, Long currentUserId);
}
