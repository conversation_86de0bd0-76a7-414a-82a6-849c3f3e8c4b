package com.example.social.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.dto.UserDTO;
import com.example.social.entity.Friendship;

import java.util.List;
import java.util.Map;

public interface FriendshipService extends IService<Friendship> {

    /**
     * 获取关注列表和粉丝列表
     * @return Map包含关注列表和粉丝列表
     */
    Map<String, List<UserDTO>> getFriendList();

    /**
     * 关注用户
     * @param userId 要关注的用户ID
     */
    void addFriend(Long userId);

    /**
     * 取消关注
     * @param userId 要取消关注的用户ID
     */
    void deleteFriend(Long userId);

    /**
     * 检查是否已关注
     * @param userId 当前用户ID
     * @param followId 关注的用户ID
     * @return 是否已关注
     */
    boolean hasFollowed(Long userId, Long followId);

    /**
     * 检查两个用户是否互为好友
     * @param userId 用户ID
     * @param targetId 目标用户ID
     * @return 是否互为好友
     */
    boolean isFriend(Long userId, Long targetId);
}