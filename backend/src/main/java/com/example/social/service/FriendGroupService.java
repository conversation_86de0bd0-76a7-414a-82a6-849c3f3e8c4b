package com.example.social.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.entity.FriendGroup;
import com.example.social.entity.User;

import java.util.List;
import java.util.Map;

public interface FriendGroupService extends IService<FriendGroup> {
    /**
     * 创建好友分组
     */
    FriendGroup createGroup(Long userId, String groupName, String description);

    /**
     * 修改分组信息
     */
    void updateGroup(FriendGroup group);

    /**
     * 删除分组
     */
    void deleteGroup(Long groupId);

    /**
     * 获取用户的所有分组
     */
    List<FriendGroup> getUserGroups(Long userId);

    /**
     * 将好友添加到分组
     */
    void addFriendToGroup(Long groupId, Long friendId);

    /**
     * 从分组中移除好友
     */
    void removeFriendFromGroup(Long groupId, Long friendId);

    /**
     * 获取分组中的所有好友
     */
    List<User> getGroupFriends(Long groupId);

    /**
     * 获取好友所在的所有分组
     */
    List<FriendGroup> getFriendGroups(Long friendId);

    /**
     * 获取用户的好友分组映射
     */
    Map<Long, List<FriendGroup>> getFriendGroupsMap(Long userId);
} 