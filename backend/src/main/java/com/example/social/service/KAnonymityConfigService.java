package com.example.social.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.entity.KAnonymityConfig;

public interface KAnonymityConfigService extends IService<KAnonymityConfig> {
    
    /**
     * 获取当前生效的K匿名配置
     */
    KAnonymityConfig getCurrentConfig();
    
    /**
     * 更新K匿名配置
     */
    void updateConfig(KAnonymityConfig config);
    
    /**
     * 启用/禁用配置
     */
    void toggleConfig(Long configId, Boolean enabled);
} 