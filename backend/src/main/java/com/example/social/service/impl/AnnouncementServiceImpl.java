package com.example.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.social.common.BusinessException;
import com.example.social.dto.AnnouncementDTO;
import com.example.social.dto.AnnouncementCommentDTO;
import com.example.social.dto.UserDTO;
import com.example.social.entity.*;
import com.example.social.mapper.*;
import com.example.social.security.UserDetailsImpl;
import com.example.social.service.AnnouncementService;
import com.example.social.service.SensitiveWordService;
import com.example.social.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公告服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnouncementServiceImpl extends ServiceImpl<AnnouncementMapper, Announcement> implements AnnouncementService {

    private final UserService userService;
    private final AnnouncementCommentMapper commentMapper;
    private final AnnouncementLikeMapper likeMapper;
    private final AnnouncementCommentLikeMapper commentLikeMapper;
    private final SensitiveWordService sensitiveWordService;
    private final UserMapper userMapper;


    @Override
    public Page<AnnouncementDTO> page(int pageNum, int pageSize, String category, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<Announcement> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Announcement::getStatus, "PUBLISHED");

        if (StringUtils.hasText(category)) {
            wrapper.eq(Announcement::getCategory, category);
        }

        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Announcement::getTitle, keyword)
                    .or().like(Announcement::getContent, keyword));
        }

        // 按置顶和创建时间排序
        wrapper.orderByDesc(Announcement::getIsTop)
                .orderByDesc(Announcement::getCreateTime);

        // 执行分页查询
        Page<Announcement> page = page(new Page<>(pageNum, pageSize), wrapper);

        // 转换为DTO
        return convertToPageDTO(page);
    }

    @Override
    public Page<AnnouncementDTO> adminPage(int pageNum, int pageSize, String category, String status, String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<Announcement> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(category)) {
            wrapper.eq(Announcement::getCategory, category);
        }

        if (StringUtils.hasText(status)) {
            wrapper.eq(Announcement::getStatus, status);
        }

        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Announcement::getTitle, keyword)
                    .or().like(Announcement::getContent, keyword));
        }

        // 按置顶和创建时间排序
        wrapper.orderByDesc(Announcement::getIsTop)
                .orderByDesc(Announcement::getCreateTime);

        // 执行分页查询
        Page<Announcement> page = page(new Page<>(pageNum, pageSize), wrapper);

        // 转换为DTO
        return convertToPageDTO(page);
    }

    @Override
    public AnnouncementDTO getById(Long id) {
        Announcement announcement = baseMapper.selectById(id);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }

        // 增加浏览次数
        announcement.setViewCount(announcement.getViewCount() + 1);
        updateById(announcement);

        return convertToDTO(announcement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnouncementDTO create(AnnouncementDTO dto) {
        // 获取当前用户
        User currentUser = getCurrentUser();
        if (!"ADMIN".equals(currentUser.getRole())) {
            throw new BusinessException("只有管理员可以发布公告");
        }

        // 过滤敏感词
        String filteredTitle = sensitiveWordService.filter(dto.getTitle());
        String filteredContent = sensitiveWordService.filter(dto.getContent());

        // 创建公告
        Announcement announcement = new Announcement();
        BeanUtils.copyProperties(dto, announcement);
        announcement.setTitle(filteredTitle);
        announcement.setContent(filteredContent);
        announcement.setAuthorId(currentUser.getId());
        announcement.setViewCount(0);
        announcement.setLikeCount(0);
        announcement.setCommentCount(0);

        if (announcement.getIsTop() == null) {
            announcement.setIsTop(false);
        }

        if (!StringUtils.hasText(announcement.getStatus())) {
            announcement.setStatus("PUBLISHED");
        }

        save(announcement);

        // 重新查询以获取完整的数据（包括自动生成的时间戳）
        Announcement savedAnnouncement = baseMapper.selectById(announcement.getId());
        return convertToDTO(savedAnnouncement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnouncementDTO update(AnnouncementDTO dto) {
        // 获取当前用户
        User currentUser = getCurrentUser();
        if (!"ADMIN".equals(currentUser.getRole())) {
            throw new BusinessException("只有管理员可以修改公告");
        }

        // 检查公告是否存在
        Announcement announcement = baseMapper.selectById(dto.getId());
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }

        // 过滤敏感词
        String filteredTitle = sensitiveWordService.filter(dto.getTitle());
        String filteredContent = sensitiveWordService.filter(dto.getContent());

        // 更新公告
        announcement.setTitle(filteredTitle);
        announcement.setContent(filteredContent);
        announcement.setCategory(dto.getCategory());
        announcement.setStatus(dto.getStatus());
        announcement.setIsTop(dto.getIsTop());

        updateById(announcement);

        return convertToDTO(announcement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 获取当前用户
        User currentUser = getCurrentUser();
        if (!"ADMIN".equals(currentUser.getRole())) {
            throw new BusinessException("只有管理员可以删除公告");
        }

        // 检查公告是否存在
        Announcement announcement = baseMapper.selectById(id);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }

        // 删除相关评论
        LambdaQueryWrapper<AnnouncementComment> commentWrapper = new LambdaQueryWrapper<>();
        commentWrapper.eq(AnnouncementComment::getAnnouncementId, id);
        commentMapper.delete(commentWrapper);

        // 删除相关点赞
        LambdaQueryWrapper<AnnouncementLike> likeWrapper = new LambdaQueryWrapper<>();
        likeWrapper.eq(AnnouncementLike::getAnnouncementId, id);
        likeMapper.delete(likeWrapper);

        // 删除公告
        removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleLike(Long id) {
        // 获取当前用户
        User currentUser = getCurrentUser();

        // 检查公告是否存在
        Announcement announcement = baseMapper.selectById(id);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<AnnouncementLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnnouncementLike::getAnnouncementId, id)
                .eq(AnnouncementLike::getUserId, currentUser.getId());

        AnnouncementLike existingLike = likeMapper.selectOne(wrapper);

        if (existingLike != null) {
            // 取消点赞
            likeMapper.deleteById(existingLike.getId());
            announcement.setLikeCount(announcement.getLikeCount() - 1);
        } else {
            // 点赞
            AnnouncementLike like = new AnnouncementLike();
            like.setAnnouncementId(id);
            like.setUserId(currentUser.getId());
            likeMapper.insert(like);
            announcement.setLikeCount(announcement.getLikeCount() + 1);
        }

        updateById(announcement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnouncementCommentDTO addComment(Long announcementId, String content, Long parentId) {
        // 获取当前用户
        User currentUser = getCurrentUser();

        // 检查公告是否存在
        Announcement announcement = baseMapper.selectById(announcementId);
        if (announcement == null) {
            throw new BusinessException("公告不存在");
        }

        // 过滤敏感词
        String filteredContent = sensitiveWordService.filter(content);

        // 创建评论
        AnnouncementComment comment = new AnnouncementComment();
        comment.setAnnouncementId(announcementId);
        comment.setUserId(currentUser.getId());
        comment.setContent(filteredContent);
        comment.setParentId(parentId);
        comment.setLikeCount(0);

        commentMapper.insert(comment);

        // 更新公告评论数
        announcement.setCommentCount(announcement.getCommentCount() + 1);
        updateById(announcement);

        return convertToCommentDTO(comment, currentUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteComment(Long commentId) {
        // 获取当前用户
        User currentUser = getCurrentUser();

        // 获取评论
        AnnouncementComment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        // 检查是否是评论作者
        if (!comment.getUserId().equals(currentUser.getId())) {
            throw new BusinessException("无权删除该评论");
        }

        deleteCommentAndUpdateCount(comment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adminDeleteComment(Long commentId) {
        // 获取当前用户
        User currentUser = getCurrentUser();
        if (!"ADMIN".equals(currentUser.getRole())) {
            throw new BusinessException("只有管理员可以执行此操作");
        }

        // 获取评论
        AnnouncementComment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        deleteCommentAndUpdateCount(comment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleCommentLike(Long commentId) {
        // 获取当前用户
        User currentUser = getCurrentUser();

        // 检查评论是否存在
        AnnouncementComment comment = commentMapper.selectById(commentId);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }

        // 检查是否已点赞
        LambdaQueryWrapper<AnnouncementCommentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnnouncementCommentLike::getCommentId, commentId)
                .eq(AnnouncementCommentLike::getUserId, currentUser.getId());

        AnnouncementCommentLike existingLike = commentLikeMapper.selectOne(wrapper);

        if (existingLike != null) {
            // 取消点赞
            commentLikeMapper.deleteById(existingLike.getId());
            comment.setLikeCount(comment.getLikeCount() - 1);
        } else {
            // 点赞
            AnnouncementCommentLike like = new AnnouncementCommentLike();
            like.setCommentId(commentId);
            like.setUserId(currentUser.getId());
            commentLikeMapper.insert(like);
            comment.setLikeCount(comment.getLikeCount() + 1);
        }

        commentMapper.updateById(comment);
    }

    @Override
    public Page<AnnouncementCommentDTO> getComments(Long announcementId, int pageNum, int pageSize) {
        // 查询顶级评论
        LambdaQueryWrapper<AnnouncementComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnnouncementComment::getAnnouncementId, announcementId)
                .isNull(AnnouncementComment::getParentId)
                .orderByDesc(AnnouncementComment::getCreateTime);

        Page<AnnouncementComment> page = commentMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);

        // 转换为DTO
        Page<AnnouncementCommentDTO> dtoPage = new Page<>();
        dtoPage.setTotal(page.getTotal());
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());

        List<AnnouncementCommentDTO> records = new ArrayList<>();
        for (AnnouncementComment comment : page.getRecords()) {
            User user = userMapper.selectById(comment.getUserId());
            AnnouncementCommentDTO dto = convertToCommentDTO(comment, user);

            // 查询子评论
            LambdaQueryWrapper<AnnouncementComment> replyWrapper = new LambdaQueryWrapper<>();
            replyWrapper.eq(AnnouncementComment::getParentId, comment.getId())
                    .orderByAsc(AnnouncementComment::getCreateTime);
            List<AnnouncementComment> replies = commentMapper.selectList(replyWrapper);

            List<AnnouncementCommentDTO> replyDTOs = new ArrayList<>();
            for (AnnouncementComment reply : replies) {
                User replyUser =userMapper.selectById(reply.getUserId());
                replyDTOs.add(convertToCommentDTO(reply, replyUser));
            }
            dto.setReplies(replyDTOs);

            records.add(dto);
        }

        dtoPage.setRecords(records);
        return dtoPage;
    }

    /**
     * 删除评论并更新计数
     */
    private void deleteCommentAndUpdateCount(AnnouncementComment comment) {
        // 删除子评论
        LambdaQueryWrapper<AnnouncementComment> replyWrapper = new LambdaQueryWrapper<>();
        replyWrapper.eq(AnnouncementComment::getParentId, comment.getId());
        List<AnnouncementComment> replies = commentMapper.selectList(replyWrapper);

        int deletedCount = 1 + replies.size(); // 包括主评论

        // 删除评论点赞
        LambdaQueryWrapper<AnnouncementCommentLike> likeWrapper = new LambdaQueryWrapper<>();
        likeWrapper.eq(AnnouncementCommentLike::getCommentId, comment.getId());
        commentLikeMapper.delete(likeWrapper);

        // 删除子评论的点赞
        for (AnnouncementComment reply : replies) {
            LambdaQueryWrapper<AnnouncementCommentLike> replyLikeWrapper = new LambdaQueryWrapper<>();
            replyLikeWrapper.eq(AnnouncementCommentLike::getCommentId, reply.getId());
            commentLikeMapper.delete(replyLikeWrapper);
        }

        // 删除评论
        commentMapper.deleteById(comment.getId());
        if (!replies.isEmpty()) {
            commentMapper.delete(replyWrapper);
        }

        // 更新公告评论数
        Announcement announcement = baseMapper.selectById(comment.getAnnouncementId());
        announcement.setCommentCount(announcement.getCommentCount() - deletedCount);
        updateById(announcement);
    }

    /**
     * 获取当前用户
     */
    private User getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
            UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
            return userDetails.getUser();
        }
        throw new BusinessException("未登录或登录已过期");
    }

    /**
     * 转换为分页DTO
     */
    private Page<AnnouncementDTO> convertToPageDTO(Page<Announcement> page) {
        Page<AnnouncementDTO> dtoPage = new Page<>();
        dtoPage.setTotal(page.getTotal());
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());

        List<AnnouncementDTO> records = page.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        dtoPage.setRecords(records);
        return dtoPage;
    }

    /**
     * 转换为DTO
     */
    private AnnouncementDTO convertToDTO(Announcement announcement) {
        AnnouncementDTO dto = new AnnouncementDTO();
        BeanUtils.copyProperties(announcement, dto);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (announcement.getCreateTime() != null) {
            dto.setCreateTime(announcement.getCreateTime().format(formatter));
        }
        if (announcement.getUpdateTime() != null) {
            dto.setUpdateTime(announcement.getUpdateTime().format(formatter));
        }

        // 设置作者信息
        if (announcement.getAuthorId() != null) {
            User author = userMapper.selectById(announcement.getAuthorId());
            if (author != null) {
                UserDTO authorDTO = userService.convertToDTO(author);
                dto.setAuthor(authorDTO);
            }
        }

        // 检查当前用户是否已点赞
        try {
            User currentUser = getCurrentUser();
            LambdaQueryWrapper<AnnouncementLike> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AnnouncementLike::getAnnouncementId, announcement.getId())
                    .eq(AnnouncementLike::getUserId, currentUser.getId());
            dto.setLiked(likeMapper.selectCount(wrapper) > 0);
        } catch (Exception e) {
            dto.setLiked(false);
        }

        return dto;
    }

    /**
     * 转换评论为DTO
     */
    private AnnouncementCommentDTO convertToCommentDTO(AnnouncementComment comment, User user) {
        AnnouncementCommentDTO dto = new AnnouncementCommentDTO();
        BeanUtils.copyProperties(comment, dto);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (comment.getCreateTime() != null) {
            dto.setCreateTime(comment.getCreateTime().format(formatter));
        }
        if (comment.getUpdateTime() != null) {
            dto.setUpdateTime(comment.getUpdateTime().format(formatter));
        }

        // 设置用户信息
        if (user != null) {
            UserDTO userDTO = userService.convertToDTO(user);
            dto.setUser(userDTO);
        }

        // 检查当前用户是否已点赞
        try {
            User currentUser = getCurrentUser();
            LambdaQueryWrapper<AnnouncementCommentLike> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AnnouncementCommentLike::getCommentId, comment.getId())
                    .eq(AnnouncementCommentLike::getUserId, currentUser.getId());
            dto.setLiked(commentLikeMapper.selectCount(wrapper) > 0);
        } catch (Exception e) {
            dto.setLiked(false);
        }

        return dto;
    }
}
