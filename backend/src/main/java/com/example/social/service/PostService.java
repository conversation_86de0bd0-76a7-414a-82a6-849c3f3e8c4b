package com.example.social.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.dto.CommentDTO;
import com.example.social.dto.PostDTO;
import com.example.social.entity.Post;

import java.util.List;

public interface PostService extends IService<Post> {

    /**
     * 创建动态
     */
    PostDTO createPost(String content, List<String> images);

    /**
     * 获取动态列表
     */
    IPage<PostDTO> getPosts(Long page, Long size);

    /**
     * 点赞/取消点赞
     */
    void toggleLike(Long postId);

    /**
     * 评论动态
     */
    void comment(Long postId, String content);

    /**
     * 获取动态的评论列表
     */
    List<CommentDTO> getComments(Long postId);

    /**
     * 删除评论
     * @param commentId 评论ID
     */
    void deleteComment(Long commentId);

    /**
     * 管理员删除评论
     * @param commentId 评论ID
     */
    void adminDeleteComment(Long commentId);

    /**
     * 管理员删除动态
     * @param postId 动态ID
     */
    void adminDeletePost(Long postId);
}