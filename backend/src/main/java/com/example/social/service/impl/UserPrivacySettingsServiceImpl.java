package com.example.social.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.social.common.BusinessException;
import com.example.social.dto.PrivacySettingsDTO;
import com.example.social.dto.UpdatePrivacySettingsRequest;
import com.example.social.dto.UserDTO;
import com.example.social.entity.UserPrivacySettings;
import com.example.social.enums.PrivacyLevel;
import com.example.social.mapper.UserPrivacySettingsMapper;
import com.example.social.service.FriendshipService;
import com.example.social.service.UserPrivacySettingsService;
import com.example.social.security.UserDetailsImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.event.EventListener;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.example.social.event.UserInfoRequestEvent;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class UserPrivacySettingsServiceImpl extends ServiceImpl<UserPrivacySettingsMapper, UserPrivacySettings> implements UserPrivacySettingsService {

    private final FriendshipService friendshipService;

    // 字段名到隐私设置属性的映射
    private static final Map<String, String> FIELD_TO_PRIVACY_SETTING = new HashMap<>();

    static {
        FIELD_TO_PRIVACY_SETTING.put("nickname", "profileVisibility");
        FIELD_TO_PRIVACY_SETTING.put("avatar", "profileVisibility");
        FIELD_TO_PRIVACY_SETTING.put("realName", "realNameVisibility");
        FIELD_TO_PRIVACY_SETTING.put("phone", "phoneVisibility");
        FIELD_TO_PRIVACY_SETTING.put("address", "addressVisibility");
    }

    @Override
    public PrivacySettingsDTO getCurrentUserPrivacySettings() {
        // 获取当前用户ID
        Long currentUserId = null;
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
                UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
                currentUserId = userDetails.getUser().getId();
            }
        } catch (Exception ignored) {
            throw new BusinessException("未登录或登录已过期");
        }

        if (currentUserId == null) {
            throw new BusinessException("未登录或登录已过期");
        }

        UserPrivacySettings settings = getByUserId(currentUserId);

        if (settings == null) {
            settings = createDefaultSettings(currentUserId);
        }

        PrivacySettingsDTO dto = new PrivacySettingsDTO();
        BeanUtils.copyProperties(settings, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrivacySettingsDTO updatePrivacySettings(UpdatePrivacySettingsRequest request) {
        // 获取当前用户ID
        Long currentUserId = null;
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof UserDetailsImpl) {
                UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
                currentUserId = userDetails.getUser().getId();
            }
        } catch (Exception ignored) {
            throw new BusinessException("未登录或登录已过期");
        }

        if (currentUserId == null) {
            throw new BusinessException("未登录或登录已过期");
        }

        UserPrivacySettings settings = getByUserId(currentUserId);

        if (settings == null) {
            settings = createDefaultSettings(currentUserId);
        }

        // 更新设置
        if (request.getProfileVisibility() != null) {
            settings.setProfileVisibility(request.getProfileVisibility());
        }
        if (request.getRealNameVisibility() != null) {
            settings.setRealNameVisibility(request.getRealNameVisibility());
        }
        if (request.getPhoneVisibility() != null) {
            settings.setPhoneVisibility(request.getPhoneVisibility());
        }
        if (request.getAddressVisibility() != null) {
            settings.setAddressVisibility(request.getAddressVisibility());
        }
        if (request.getPostsVisibility() != null) {
            settings.setPostsVisibility(request.getPostsVisibility());
        }
        if (request.getFriendsListVisibility() != null) {
            settings.setFriendsListVisibility(request.getFriendsListVisibility());
        }
        if (request.getOnlineStatusVisibility() != null) {
            settings.setOnlineStatusVisibility(request.getOnlineStatusVisibility());
        }

        // 保存更新
        saveOrUpdate(settings);

        PrivacySettingsDTO dto = new PrivacySettingsDTO();
        BeanUtils.copyProperties(settings, dto);
        return dto;
    }

    @Override
    public UserPrivacySettings getByUserId(Long userId) {
        return lambdaQuery()
                .eq(UserPrivacySettings::getUserId, userId)
                .one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserPrivacySettings createDefaultSettings(Long userId) {
        UserPrivacySettings settings = new UserPrivacySettings();
        settings.setUserId(userId);
        settings.setProfileVisibility(PrivacyLevel.PUBLIC.name());
        settings.setRealNameVisibility(PrivacyLevel.FRIENDS_ONLY.name());
        settings.setPhoneVisibility(PrivacyLevel.PRIVATE.name());
        settings.setAddressVisibility(PrivacyLevel.PRIVATE.name());
        settings.setPostsVisibility(PrivacyLevel.PUBLIC.name());
        settings.setFriendsListVisibility(PrivacyLevel.PUBLIC.name());
        settings.setOnlineStatusVisibility(PrivacyLevel.PUBLIC.name());

        save(settings);
        return settings;
    }

    @Override
    public boolean canViewField(Long targetUserId, String fieldName, Long currentUserId) {
        // 如果是查看自己的信息，总是可见
        if (Objects.equals(targetUserId, currentUserId)) {
            return true;
        }

        // 获取目标用户的隐私设置
        UserPrivacySettings settings = getByUserId(targetUserId);
        if (settings == null) {
            settings = createDefaultSettings(targetUserId);
        }

        // 获取字段对应的隐私设置
        String privacySettingName = FIELD_TO_PRIVACY_SETTING.getOrDefault(fieldName, "profileVisibility");

        try {
            // 使用反射获取隐私设置值
            Field field = UserPrivacySettings.class.getDeclaredField(privacySettingName);
            field.setAccessible(true);
            String privacyLevelStr = (String) field.get(settings);
            PrivacyLevel privacyLevel = PrivacyLevel.valueOf(privacyLevelStr);

            // 根据隐私级别判断是否可见
            switch (privacyLevel) {
                case PUBLIC:
                    return true;
                case FRIENDS_ONLY:
                    // 检查是否是好友关系
                    return friendshipService.isFriend(currentUserId, targetUserId);
                case PRIVATE:
                    return false;
                default:
                    return false;
            }
        } catch (Exception e) {
            // 出现异常默认不可见
            return false;
        }
    }

    @Override
    public void applyPrivacyFilter(Long targetUserId, Object dto, Long currentUserId) {
        // 如果是查看自己的信息，不需要过滤
        if (Objects.equals(targetUserId, currentUserId)) {
            return;
        }

        // 如果DTO是UserDTO类型，应用用户信息隐私过滤
        if (dto instanceof UserDTO) {
            UserDTO userDTO = (UserDTO) dto;

            // 检查各个字段的可见性
            if (!canViewField(targetUserId, "realName", currentUserId)) {
                userDTO.setRealName(null);
            }
            if (!canViewField(targetUserId, "phone", currentUserId)) {
                userDTO.setPhone(null);
            }
            if (!canViewField(targetUserId, "address", currentUserId)) {
                userDTO.setAddress(null);
            }

            // 如果个人资料设置为私密，则隐藏昵称和头像
            if (!canViewField(targetUserId, "nickname", currentUserId)) {
                userDTO.setNickname("***");
                userDTO.setAvatar(null);
            }
        }
    }

    /**
     * 监听用户信息请求事件，应用隐私过滤
     */
    @EventListener
    public void handleUserInfoRequest(UserInfoRequestEvent event) {
        applyPrivacyFilter(event.getUserId(), event.getUserDto(), event.getCurrentUserId());
    }
}
