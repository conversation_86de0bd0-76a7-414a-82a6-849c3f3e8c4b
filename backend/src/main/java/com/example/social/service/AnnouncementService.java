package com.example.social.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.social.dto.AnnouncementDTO;
import com.example.social.dto.AnnouncementCommentDTO;

/**
 * 公告服务接口
 */
public interface AnnouncementService {
    
    /**
     * 分页查询公告列表
     */
    Page<AnnouncementDTO> page(int pageNum, int pageSize, String category, String keyword);
    
    /**
     * 管理员分页查询公告列表
     */
    Page<AnnouncementDTO> adminPage(int pageNum, int pageSize, String category, String status, String keyword);
    
    /**
     * 根据ID获取公告详情
     */
    AnnouncementDTO getById(Long id);
    
    /**
     * 创建公告（管理员）
     */
    AnnouncementDTO create(AnnouncementDTO dto);
    
    /**
     * 更新公告（管理员）
     */
    AnnouncementDTO update(AnnouncementDTO dto);
    
    /**
     * 删除公告（管理员）
     */
    void delete(Long id);
    
    /**
     * 点赞/取消点赞公告
     */
    void toggleLike(Long id);
    
    /**
     * 添加评论
     */
    AnnouncementCommentDTO addComment(Long announcementId, String content, Long parentId);
    
    /**
     * 删除评论
     */
    void deleteComment(Long commentId);
    
    /**
     * 管理员删除评论
     */
    void adminDeleteComment(Long commentId);
    
    /**
     * 点赞/取消点赞评论
     */
    void toggleCommentLike(Long commentId);
    
    /**
     * 获取公告评论列表
     */
    Page<AnnouncementCommentDTO> getComments(Long announcementId, int pageNum, int pageSize);
}
