package com.example.social.service;

import com.example.social.dto.LoginRequest;
import com.example.social.dto.RegisterRequest;
import com.example.social.dto.UserDTO;
import com.example.social.entity.User;

import java.util.List;

public interface UserService {

    /**
     * 用户注册
     */
    UserDTO register(RegisterRequest request);

    /**
     * 用户登录
     */
    String login(LoginRequest request);

    /**
     * 获取当前登录用户信息
     */
    UserDTO getCurrentUser();

    /**
     * 根据用户名查找用户
     */
    User getByUsername(String username);

    /**
     * 更新用户信息
     */
    UserDTO updateUser(User user);

    /**
     * 转换User为UserDTO
     */
    UserDTO convertToDTO(User user);

    /**
     * 根据用户ID获取用户信息，并应用隐私设置
     */
    UserDTO getUserInfo(Long userId);

    /**
     * 根据昵称搜索用户
     */
    List<UserDTO> searchByNickname(String nickname);
}