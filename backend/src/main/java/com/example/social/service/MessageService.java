package com.example.social.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.entity.Message;

import java.util.List;

public interface MessageService extends IService<Message> {
    /**
     * 发送消息
     */
    Message sendMessage(Long fromUserId, Long toUserId, String content, String messageType);

    /**
     * 获取与指定用户的聊天记录
     */
    Page<Message> getChatHistory(Long currentUserId, Long targetUserId, Integer pageNum, Integer pageSize);

    /**
     * 获取未读消息列表
     */
    List<Message> getUnreadMessages(Long userId);

    /**
     * 标记消息为已读
     */
    void markAsRead(Long messageId);

    /**
     * 获取最近的聊天列表
     */
    List<Message> getRecentChats(Long userId);
} 