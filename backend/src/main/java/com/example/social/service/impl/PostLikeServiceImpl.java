package com.example.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.social.entity.PostLike;
import com.example.social.mapper.PostLikeMapper;
import com.example.social.service.PostLikeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PostLikeServiceImpl extends ServiceImpl<PostLikeMapper, PostLike> implements PostLikeService {

    @Override
    public boolean hasLiked(Long postId, Long userId) {
        LambdaQueryWrapper<PostLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PostLike::getPostId, postId)
                .eq(PostLike::getUserId, userId);
        return count(wrapper) > 0;
    }

    @Override
    @Transactional
    public boolean toggleLike(Long postId, Long userId) {
        LambdaQueryWrapper<PostLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PostLike::getPostId, postId)
                .eq(PostLike::getUserId, userId);
        
        PostLike like = getOne(wrapper);

        if (like == null) {
            // 添加点赞
            like = new PostLike();
            like.setPostId(postId);
            like.setUserId(userId);
            save(like);
            return true;
        } else {
            // 取消点赞
            removeById(like.getId());
            return false;
        }
    }
} 