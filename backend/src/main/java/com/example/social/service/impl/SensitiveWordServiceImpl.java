package com.example.social.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.social.common.BusinessException;
import com.example.social.dto.SensitiveWordDTO;
import com.example.social.entity.SensitiveWord;
import com.example.social.mapper.SensitiveWordMapper;
import com.example.social.service.SensitiveWordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 敏感词服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SensitiveWordServiceImpl extends ServiceImpl<SensitiveWordMapper, SensitiveWord> implements SensitiveWordService {

    /**
     * 敏感词字典树
     */
    private final Map<Character, Map> sensitiveWordTrie = new ConcurrentHashMap<>();

    /**
     * 初始化敏感词字典树
     */
    @PostConstruct
    public void init() {
        // 加载所有敏感词
        List<SensitiveWord> sensitiveWords = getAllSensitiveWords();

        // 构建字典树
        for (SensitiveWord word : sensitiveWords) {
            addWordToTrie(word.getWord());
        }
    }

    /**
     * 将敏感词添加到字典树
     */
    private void addWordToTrie(String word) {
        Map<Character, Map> currentMap = sensitiveWordTrie;

        for (int i = 0; i < word.length(); i++) {
            char c = word.charAt(i);

            // 获取当前字符的子节点
            Map<Character, Map> subMap = currentMap.get(c);

            // 如果子节点不存在，创建新的子节点
            if (subMap == null) {
                subMap = new HashMap<>();
                currentMap.put(c, subMap);
            }

            // 移动到下一个节点
            currentMap = subMap;

            // 如果是最后一个字符，标记为敏感词结束
            if (i == word.length() - 1) {
                currentMap.put('\\', null);
            }
        }
    }

    @Override
    public Page<SensitiveWordDTO> page(int pageNum, int pageSize, String keyword, String category) {
        // 构建查询条件
        LambdaQueryWrapper<SensitiveWord> wrapper = new LambdaQueryWrapper<>();

        // 添加关键词搜索条件
        if (StringUtils.hasText(keyword)) {
            wrapper.like(SensitiveWord::getWord, keyword);
        }

        // 添加分类搜索条件
        if (StringUtils.hasText(category)) {
            wrapper.eq(SensitiveWord::getCategory, category);
        }

        // 按创建时间降序排序
        wrapper.orderByDesc(SensitiveWord::getCreateTime);

        // 执行分页查询
        Page<SensitiveWord> page = page(new Page<>(pageNum, pageSize), wrapper);

        // 转换为DTO
        Page<SensitiveWordDTO> dtoPage = new Page<>();
        dtoPage.setTotal(page.getTotal());
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<SensitiveWordDTO> records = page.getRecords().stream().map(word -> {
            SensitiveWordDTO dto = new SensitiveWordDTO();
            BeanUtils.copyProperties(word, dto);
            dto.setCreateTime(word.getCreateTime().format(formatter));
            return dto;
        }).collect(Collectors.toList());

        dtoPage.setRecords(records);

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(SensitiveWordDTO dto) {
        // 检查敏感词是否已存在
        LambdaQueryWrapper<SensitiveWord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SensitiveWord::getWord, dto.getWord());

        if (count(wrapper) > 0) {
            throw new BusinessException("敏感词已存在");
        }

        // 创建敏感词实体
        SensitiveWord sensitiveWord = new SensitiveWord();
        BeanUtils.copyProperties(dto, sensitiveWord);

        // 保存敏感词
        save(sensitiveWord);

        // 更新字典树
        addWordToTrie(sensitiveWord.getWord());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SensitiveWordDTO dto) {
        // 检查敏感词是否存在
        SensitiveWord sensitiveWord = getById(dto.getId());
        if (sensitiveWord == null) {
            throw new BusinessException("敏感词不存在");
        }

        // 如果修改了敏感词内容，需要检查新的敏感词是否已存在
        if (!sensitiveWord.getWord().equals(dto.getWord())) {
            LambdaQueryWrapper<SensitiveWord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SensitiveWord::getWord, dto.getWord());

            if (count(wrapper) > 0) {
                throw new BusinessException("敏感词已存在");
            }
        }

        // 更新敏感词
        BeanUtils.copyProperties(dto, sensitiveWord);
        updateById(sensitiveWord);

        // 重新初始化字典树
        init();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        // 检查敏感词是否存在
        SensitiveWord sensitiveWord = getById(id);
        if (sensitiveWord == null) {
            throw new BusinessException("敏感词不存在");
        }

        // 删除敏感词
        removeById(id);

        // 重新初始化字典树
        init();
    }

    @Override
    public int batchImport(List<String> words, String category, Integer level) {
        if (words == null || words.isEmpty()) {
            return 0;
        }

        // 过滤掉已存在的敏感词
        LambdaQueryWrapper<SensitiveWord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SensitiveWord::getWord, words);
        List<String> existingWords = list(wrapper).stream()
                .map(SensitiveWord::getWord)
                .collect(Collectors.toList());

        // 过滤重复的词
        List<String> newWords = words.stream()
                .filter(word -> !existingWords.contains(word))
                .distinct() // 确保列表中没有重复项
                .collect(Collectors.toList());

        if (newWords.isEmpty()) {
            return 0;
        }

        int successCount = 0;

        // 逐个保存，避免批量操作中的异常导致所有操作失败
        for (String word : newWords) {
            try {
                SensitiveWord sensitiveWord = new SensitiveWord();
                sensitiveWord.setWord(word);
                sensitiveWord.setCategory(category);
                sensitiveWord.setLevel(level);

                save(sensitiveWord);
                addWordToTrie(word);
                successCount++;
            } catch (Exception e) {
                // 记录异常但不中断处理
                log.error("保存敏感词 '{}' 失败: {}", word, e.getMessage());
            }
        }

        return successCount;
    }

    @Override
    public String filter(String text) {
        if (!StringUtils.hasText(text)) {
            return text;
        }

        StringBuilder result = new StringBuilder(text);
        int i = 0;

        while (i < result.length()) {
            int length = checkSensitiveWord(result.toString(), i);
            if (length > 0) {
                // 将敏感词替换为*
                for (int j = 0; j < length; j++) {
                    result.setCharAt(i + j, '*');
                }
                i += length;
            } else {
                i++;
            }
        }

        return result.toString();
    }

    @Override
    public boolean containsSensitiveWords(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }

        for (int i = 0; i < text.length(); i++) {
            int length = checkSensitiveWord(text, i);
            if (length > 0) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<SensitiveWord> getAllSensitiveWords() {
        return list();
    }

    /**
     * 检查文本中是否包含敏感词，返回敏感词长度
     */
    private int checkSensitiveWord(String text, int beginIndex) {
        Map<Character, Map> currentMap = sensitiveWordTrie;
        int wordLength = 0;

        for (int i = beginIndex; i < text.length(); i++) {
            char c = text.charAt(i);

            // 获取当前字符的子节点
            currentMap = currentMap.get(c);

            // 如果子节点不存在，说明不是敏感词
            if (currentMap == null) {
                break;
            }

            // 单词长度加1
            wordLength++;

            // 如果包含结束标记，说明找到了敏感词
            if (currentMap.containsKey('\\')) {
                return wordLength;
            }
        }

        // 如果没有找到敏感词，返回0
        return 0;
    }
}
