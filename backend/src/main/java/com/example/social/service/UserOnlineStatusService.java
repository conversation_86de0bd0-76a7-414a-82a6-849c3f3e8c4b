package com.example.social.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.social.entity.UserOnlineStatus;

import java.util.List;
import java.util.Map;

public interface UserOnlineStatusService extends IService<UserOnlineStatus> {
    /**
     * 更新用户在线状态
     */
    void updateOnlineStatus(Long userId, Boolean isOnline);

    /**
     * 更新用户最后活动时间
     */
    void updateLastActiveTime(Long userId);

    /**
     * 获取用户在线状态
     */
    UserOnlineStatus getUserStatus(Long userId);

    /**
     * 批量获取用户在线状态
     */
    Map<Long, Boolean> batchGetOnlineStatus(List<Long> userIds);

    /**
     * 获取所有在线用户
     */
    List<UserOnlineStatus> getOnlineUsers();

    /**
     * 清理过期的在线状态（超过指定时间未活动的用户）
     */
    void cleanExpiredStatus();
} 