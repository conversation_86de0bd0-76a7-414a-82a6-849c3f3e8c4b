package com.example.social.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 敏感词DTO
 */
@Data
public class SensitiveWordDTO {
    
    private Long id;
    
    @NotBlank(message = "敏感词不能为空")
    private String word;
    
    @Pattern(regexp = "POLITICS|PORN|ABUSE|OTHERS", message = "分类值无效")
    private String category;
    
    private Integer level;
    
    private String createTime;
}
