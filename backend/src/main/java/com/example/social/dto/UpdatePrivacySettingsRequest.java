package com.example.social.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class UpdatePrivacySettingsRequest {
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "个人资料可见性设置无效")
    private String profileVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "真实姓名可见性设置无效")
    private String realNameVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "手机号可见性设置无效")
    private String phoneVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "地址可见性设置无效")
    private String addressVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "动态可见性设置无效")
    private String postsVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "好友列表可见性设置无效")
    private String friendsListVisibility;
    
    @Pattern(regexp = "PUBLIC|FRIENDS_ONLY|PRIVATE", message = "在线状态可见性设置无效")
    private String onlineStatusVisibility;
}
