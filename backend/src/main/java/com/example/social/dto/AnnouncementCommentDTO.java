package com.example.social.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 公告评论DTO
 */
@Data
public class AnnouncementCommentDTO {
    
    private Long id;
    
    private Long announcementId;
    
    private Long userId;
    
    @NotBlank(message = "评论内容不能为空")
    private String content;
    
    private Long parentId;
    
    private Integer likeCount;
    
    private String createTime;
    
    private String updateTime;
    
    /**
     * 评论用户信息
     */
    private UserDTO user;
    
    /**
     * 是否已点赞
     */
    private Boolean liked;
    
    /**
     * 子评论列表
     */
    private List<AnnouncementCommentDTO> replies;
}
