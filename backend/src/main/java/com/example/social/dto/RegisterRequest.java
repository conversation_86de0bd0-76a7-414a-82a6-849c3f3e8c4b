package com.example.social.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class RegisterRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @NotBlank(message = "昵称不能为空")
//    @Size(min = 2, max = 20, message = "昵称长度必须在2-20个字符之间")
    private String nickname;

    @Size(min = 2, max = 20, message = "姓名长度必须在2-20个字符之间")
    private String realName;

    private String phone;

    @Size(max = 100, message = "地址长度不能超过100个字符")
    private String address;


    public String getNickname() {
        return realName;
    }
}