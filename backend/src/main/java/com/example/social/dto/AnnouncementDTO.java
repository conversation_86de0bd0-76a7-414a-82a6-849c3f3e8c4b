package com.example.social.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 公告DTO
 */
@Data
public class AnnouncementDTO {
    
    private Long id;
    
    @NotBlank(message = "标题不能为空")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    private String content;
    
    @Pattern(regexp = "NEWS|ENTERTAINMENT|LIVELIHOOD|POLICY", message = "分类值无效")
    private String category;
    
    private Long authorId;
    
    @Pattern(regexp = "DRAFT|PUBLISHED|ARCHIVED", message = "状态值无效")
    private String status;
    
    private Integer viewCount;
    
    private Integer likeCount;
    
    private Integer commentCount;
    
    private Boolean isTop;
    
    private String createTime;
    
    private String updateTime;
    
    /**
     * 发布者信息
     */
    private UserDTO author;
    
    /**
     * 是否已点赞
     */
    private Boolean liked;
    
    /**
     * 评论列表
     */
    private List<AnnouncementCommentDTO> comments;
}
