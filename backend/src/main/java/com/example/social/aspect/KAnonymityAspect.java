package com.example.social.aspect;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.example.social.dto.CommentDTO;
import com.example.social.dto.PostDTO;
import com.example.social.dto.UserDTO;
import com.example.social.entity.KAnonymityConfig;
import com.example.social.service.KAnonymityConfigService;
import com.example.social.util.KAnonymityUtil;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Aspect
@Component
@RequiredArgsConstructor
public class KAnonymityAspect {

    private final KAnonymityConfigService kAnonymityConfigService;

    /**
     * 处理返回单个用户信息的方法
     */
    @AfterReturning(
            pointcut = "execution(* com.example.social.service.*.getUserInfo(..)) || " +
                      "execution(* com.example.social.service.*.getUserProfile(..))",
            returning = "result"
    )
    public void processUserInfo(Object result) {
        if (result instanceof UserDTO) {
            KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
            KAnonymityUtil.anonymizeUserInfo((UserDTO) result, config);
        }
    }

    /**
     * 处理返回用户列表的方法
     */
    @AfterReturning(
            pointcut = "execution(* com.example.social.service.impl.FriendshipServiceImpl.getFriendList(..)) || " +
                      "execution(* com.example.social.service.impl.UserServiceImpl.searchByNickname(..))",
            returning = "result"
    )
    @SuppressWarnings("unchecked")
    public void processUserList(Object result) {
        KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
        
        // 处理Map类型返回值（好友列表）
        if (result instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) result;
            if (map.containsKey("friends") && map.get("friends") instanceof List) {
                List<UserDTO> friends = (List<UserDTO>) map.get("friends");
                if (!friends.isEmpty() && friends.get(0) != null) {
                    KAnonymityUtil.anonymizeUserList(friends, config);
                }
                if (map.containsKey("requests") && map.get("requests") instanceof List) {
                    List<UserDTO> requests = (List<UserDTO>) map.get("requests");
                    if (!requests.isEmpty() && requests.get(0) != null) {
                        KAnonymityUtil.anonymizeUserList(requests, config);
                    }
                }
            }
            return;
        }
        
        // 处理List类型返回值
        if (result instanceof List<?> && !((List<?>) result).isEmpty() 
                && ((List<?>) result).get(0) instanceof UserDTO) {
            List<UserDTO> users = (List<UserDTO>) result;
            KAnonymityUtil.anonymizeUserList(users, config);
        }
    }

    /**
     * 处理返回单个帖子的方法
     */
    @AfterReturning(
            pointcut = "execution(* com.example.social.service.*.getPostById(..)) || " +
                      "execution(* com.example.social.service.*.createPost(..))",
            returning = "result"
    )
    public void processPostInfo(Object result) {
        if (result instanceof PostDTO) {
            KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
            KAnonymityUtil.anonymizePostInfo((PostDTO) result, config);
        }
    }

    /**
     * 处理返回帖子列表的方法
     */
    @AfterReturning(
            pointcut = "execution(* com.example.social.service.impl.PostServiceImpl.getPosts(..))",
            returning = "result"
    )
    @SuppressWarnings("unchecked")
    public void processPostList(Object result) {
        if (result instanceof IPage) {
            KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
            IPage<PostDTO> page = (IPage<PostDTO>) result;
            if (page.getRecords() != null && !page.getRecords().isEmpty()) {
                KAnonymityUtil.anonymizePostList(page.getRecords(), config);
            }
            return;
        }
        
        if (result instanceof List<?> && !((List<?>) result).isEmpty() 
                && ((List<?>) result).get(0) instanceof PostDTO) {
            KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
            List<PostDTO> posts = (List<PostDTO>) result;
            KAnonymityUtil.anonymizePostList(posts, config);
        }
    }

    /**
     * 处理返回评论列表的方法
     */
    @AfterReturning(
            pointcut = "execution(* com.example.social.service.impl.PostServiceImpl.getComments(..))",
            returning = "result"
    )
    @SuppressWarnings("unchecked")
    public void processCommentList(Object result) {
        if (result instanceof List<?> && !((List<?>) result).isEmpty() 
                && ((List<?>) result).get(0) instanceof CommentDTO) {
            KAnonymityConfig config = kAnonymityConfigService.getCurrentConfig();
            List<CommentDTO> comments = (List<CommentDTO>) result;
            comments.forEach(comment -> {
                if (comment.getUser() != null) {
                    KAnonymityUtil.anonymizeUserInfo(comment.getUser(), config);
                }
            });
        }
    }
} 