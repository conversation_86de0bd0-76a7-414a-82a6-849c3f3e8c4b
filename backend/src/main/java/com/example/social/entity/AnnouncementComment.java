package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 公告评论实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("announcement_comment")
public class AnnouncementComment extends BaseEntity {
    
    /**
     * 公告ID
     */
    private Long announcementId;
    
    /**
     * 评论用户ID
     */
    private Long userId;
    
    /**
     * 评论内容
     */
    private String content;
    
    /**
     * 父评论ID（回复功能）
     */
    private Long parentId;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论用户信息（非数据库字段）
     */
    @TableField(exist = false)
    private User user;
    
    /**
     * 子评论列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<AnnouncementComment> replies;
}
