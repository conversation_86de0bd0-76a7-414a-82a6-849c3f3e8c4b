package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公告点赞实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("announcement_like")
public class AnnouncementLike extends BaseEntity {
    
    /**
     * 公告ID
     */
    private Long announcementId;
    
    /**
     * 用户ID
     */
    private Long userId;
}
