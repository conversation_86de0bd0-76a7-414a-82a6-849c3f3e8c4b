package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("message")
public class Message extends BaseEntity {
    private Long fromUserId;
    private Long toUserId;
    private String content;
    private Boolean isRead;
    private String messageType; // TEXT, IMAGE, etc.
} 