package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公告实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("announcement")
public class Announcement extends BaseEntity {
    
    /**
     * 公告标题
     */
    private String title;
    
    /**
     * 公告内容
     */
    private String content;
    
    /**
     * 分类：NEWS-热点新闻, ENTERTAINMENT-娱乐周刊, LIVELIHOOD-民生事件, POLICY-惠民政策
     */
    private String category;
    
    /**
     * 发布者ID（管理员）
     */
    private Long authorId;
    
    /**
     * 状态：DRAFT-草稿, PUBLISHED-已发布, ARCHIVED-已归档
     */
    private String status;
    
    /**
     * 浏览次数
     */
    private Integer viewCount;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 评论数
     */
    private Integer commentCount;
    
    /**
     * 是否置顶
     */
    private Boolean isTop;
    
    /**
     * 发布者信息（非数据库字段）
     */
    @TableField(exist = false)
    private User author;
}
