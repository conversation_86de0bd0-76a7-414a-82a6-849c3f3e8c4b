package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 敏感词实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sensitive_word")
public class SensitiveWord extends BaseEntity {
    
    /**
     * 敏感词
     */
    private String word;
    
    /**
     * 分类：POLITICS-政治, PORN-色情, ABUSE-辱骂, OTHERS-其他
     */
    private String category;
    
    /**
     * 敏感级别：1-低, 2-中, 3-高
     */
    private Integer level;
}
