package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "post", autoResultMap = true)
public class Post extends BaseEntity {

    private Long userId;

    private String content;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;

    private Integer likeCount;

    private Integer commentCount;

    @TableField(exist = false)
    private User user;

    @TableField(exist = false)
    private Boolean liked;
} 