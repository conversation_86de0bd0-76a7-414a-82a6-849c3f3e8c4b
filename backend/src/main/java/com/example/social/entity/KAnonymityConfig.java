package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("k_anonymity_config")
public class KAnonymityConfig extends BaseEntity {
    
    private Integer kValue;  // K匿名阈值
    
    private String strategy; // 匿名化策略
    
    private Boolean enabled; // 是否启用
    
    private String description; // 配置描述
} 