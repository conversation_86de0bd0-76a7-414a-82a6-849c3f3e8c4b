package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;
import com.example.social.entity.BaseEntity;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_online_status")
public class UserOnlineStatus extends BaseEntity {
    private Long userId;
    private Boolean isOnline;
    private LocalDateTime lastActiveTime;
    private String lastActiveIp;
    private String deviceInfo;
} 