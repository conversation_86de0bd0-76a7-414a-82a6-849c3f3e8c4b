package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公告评论点赞实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("announcement_comment_like")
public class AnnouncementCommentLike extends BaseEntity {
    
    /**
     * 评论ID
     */
    private Long commentId;
    
    /**
     * 用户ID
     */
    private Long userId;
}
