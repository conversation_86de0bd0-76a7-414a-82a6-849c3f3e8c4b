package com.example.social.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.example.social.enums.PrivacyLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_privacy_settings")
public class UserPrivacySettings extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 个人资料可见性
     */
    private String profileVisibility;
    
    /**
     * 真实姓名可见性
     */
    private String realNameVisibility;
    
    /**
     * 手机号可见性
     */
    private String phoneVisibility;
    
    /**
     * 地址可见性
     */
    private String addressVisibility;
    
    /**
     * 动态可见性
     */
    private String postsVisibility;
    
    /**
     * 好友列表可见性
     */
    private String friendsListVisibility;
    
    /**
     * 在线状态可见性
     */
    private String onlineStatusVisibility;
    
    /**
     * 获取隐私级别枚举
     */
    public PrivacyLevel getProfileVisibilityEnum() {
        return PrivacyLevel.valueOf(profileVisibility);
    }
    
    public PrivacyLevel getRealNameVisibilityEnum() {
        return PrivacyLevel.valueOf(realNameVisibility);
    }
    
    public PrivacyLevel getPhoneVisibilityEnum() {
        return PrivacyLevel.valueOf(phoneVisibility);
    }
    
    public PrivacyLevel getAddressVisibilityEnum() {
        return PrivacyLevel.valueOf(addressVisibility);
    }
    
    public PrivacyLevel getPostsVisibilityEnum() {
        return PrivacyLevel.valueOf(postsVisibility);
    }
    
    public PrivacyLevel getFriendsListVisibilityEnum() {
        return PrivacyLevel.valueOf(friendsListVisibility);
    }
    
    public PrivacyLevel getOnlineStatusVisibilityEnum() {
        return PrivacyLevel.valueOf(onlineStatusVisibility);
    }
}
