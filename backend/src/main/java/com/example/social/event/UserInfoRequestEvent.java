package com.example.social.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 用户信息请求事件，用于解耦UserService和UserPrivacySettingsService
 */
@Getter
public class UserInfoRequestEvent extends ApplicationEvent {
    private final Long userId;
    private final Long currentUserId;
    private final Object userDto;

    public UserInfoRequestEvent(Object source, Long userId, Long currentUserId, Object userDto) {
        super(source);
        this.userId = userId;
        this.currentUserId = currentUserId;
        this.userDto = userDto;
    }
}
