package com.example.social.controller;

import com.example.social.common.Result;
import com.example.social.entity.KAnonymityConfig;
import com.example.social.service.KAnonymityConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/k-anonymity")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class KAnonymityConfigController {

    private final KAnonymityConfigService kAnonymityConfigService;

    @GetMapping("/list")
    public Result<List<KAnonymityConfig>> getConfigList() {
        return Result.success(kAnonymityConfigService.list());
    }

    @GetMapping("/current")
    public Result<KAnonymityConfig> getCurrentConfig() {
        return Result.success(kAnonymityConfigService.getCurrentConfig());
    }

    @PostMapping
    public Result<Void> createConfig(@RequestBody KAnonymityConfig config) {
        kAnonymityConfigService.save(config);
        return Result.success();
    }

    @PutMapping
    public Result<Void> updateConfig(@RequestBody KAnonymityConfig config) {
        kAnonymityConfigService.updateConfig(config);
        return Result.success();
    }

    @PutMapping("/{id}/toggle")
    public Result<Void> toggleConfig(@PathVariable Long id, @RequestParam Boolean enabled) {
        kAnonymityConfigService.toggleConfig(id, enabled);
        return Result.success();
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteConfig(@PathVariable Long id) {
        kAnonymityConfigService.removeById(id);
        return Result.success();
    }
} 