package com.example.social.controller;

import com.example.social.common.Result;
import com.example.social.dto.UserDTO;
import com.example.social.entity.User;
import com.example.social.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @GetMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public Result<UserDTO> getCurrentUser() {
        return Result.success(userService.getCurrentUser());
    }

    @PutMapping("/info")
    @PreAuthorize("isAuthenticated()")
    public Result<UserDTO> updateUserInfo(@RequestBody User user) {
        // 获取当前用户
        UserDTO currentUser = userService.getCurrentUser();
        user.setId(currentUser.getId());

        // 不允许修改用户名
        user.setUsername(null);

        return Result.success(userService.updateUser(user));
    }

    @GetMapping("/{userId}")
    public Result<UserDTO> getUserInfo(@PathVariable Long userId) {
        return Result.success(userService.getUserInfo(userId));
    }

    @GetMapping("/search")
    public Result<List<UserDTO>> searchUsers(@RequestParam String nickname) {
        return Result.success(userService.searchByNickname(nickname));
    }
}