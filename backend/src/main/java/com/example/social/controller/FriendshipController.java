package com.example.social.controller;

import com.example.social.common.Result;
import com.example.social.dto.UserDTO;
import com.example.social.entity.User;
import com.example.social.service.FriendshipService;
import com.example.social.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/friend")
@RequiredArgsConstructor
@PreAuthorize("isAuthenticated()")
public class FriendshipController {

    private final FriendshipService friendshipService;
    private final UserService userService;

    @GetMapping("/list")
    public Result<Map<String, List<UserDTO>>> getFriendList() {
        return Result.success(friendshipService.getFriendList());
    }

    @PostMapping("/add")
    public Result<Void> addFriend(@RequestBody Map<String, Long> request) {
        Long userId = request.get("friendId");
        friendshipService.addFriend(userId);
        return Result.success();
    }

    @DeleteMapping("/{id}")
    public Result<Void> deleteFriend(@PathVariable Long id) {
        friendshipService.deleteFriend(id);
        return Result.success();
    }

    @GetMapping("/check/{id}")
    public Result<Boolean> hasFollowed(@PathVariable Long id) {
        // 获取当前用户
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        User currentUser = userService.getByUsername(username);

        boolean followed = friendshipService.hasFollowed(currentUser.getId(), id);
        return Result.success(followed);
    }
}