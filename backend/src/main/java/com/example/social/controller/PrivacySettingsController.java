package com.example.social.controller;

import com.example.social.common.Result;
import com.example.social.dto.PrivacySettingsDTO;
import com.example.social.dto.UpdatePrivacySettingsRequest;
import com.example.social.service.UserPrivacySettingsService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/privacy")
@RequiredArgsConstructor
public class PrivacySettingsController {

    private final UserPrivacySettingsService privacySettingsService;

    /**
     * 获取当前用户的隐私设置
     */
    @GetMapping("/settings")
    @PreAuthorize("isAuthenticated()")
    public Result<PrivacySettingsDTO> getPrivacySettings() {
        return Result.success(privacySettingsService.getCurrentUserPrivacySettings());
    }

    /**
     * 更新隐私设置
     */
    @PutMapping("/settings")
    @PreAuthorize("isAuthenticated()")
    public Result<PrivacySettingsDTO> updatePrivacySettings(@Validated @RequestBody UpdatePrivacySettingsRequest request) {
        return Result.success(privacySettingsService.updatePrivacySettings(request));
    }
}
