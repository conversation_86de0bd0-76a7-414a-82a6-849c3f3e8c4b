package com.example.social.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.social.common.Result;
import com.example.social.dto.SensitiveWordDTO;
import com.example.social.service.SensitiveWordService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 敏感词控制器
 */
@RestController
@RequestMapping("/admin/sensitive-word")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class SensitiveWordController {

    private final SensitiveWordService sensitiveWordService;

    /**
     * 分页查询敏感词
     */
    @GetMapping("/page")
    public Result<Page<SensitiveWordDTO>> page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category) {
        return Result.success(sensitiveWordService.page(pageNum, pageSize, keyword, category));
    }

    /**
     * 添加敏感词
     */
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SensitiveWordDTO dto) {
        sensitiveWordService.add(dto);
        return Result.success();
    }

    /**
     * 更新敏感词
     */
    @PutMapping
    public Result<Void> update(@Validated @RequestBody SensitiveWordDTO dto) {
        sensitiveWordService.update(dto);
        return Result.success();
    }

    /**
     * 删除敏感词
     */
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable Long id) {
        sensitiveWordService.delete(id);
        return Result.success();
    }

    /**
     * 批量导入敏感词
     */
    @PostMapping("/batch-import")
    public Result<Map<String, Integer>> batchImport(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<String> words = (List<String>) params.get("words");
        String category = (String) params.get("category");
        Integer level = (Integer) params.get("level");

        int count = sensitiveWordService.batchImport(words, category, level);
        return Result.success(Map.of("count", count));
    }

    /**
     * 测试敏感词过滤
     */
    @PostMapping("/test-filter")
    public Result<Map<String, String>> testFilter(@RequestBody Map<String, String> params) {
        String text = params.get("text");
        String filtered = sensitiveWordService.filter(text);
        return Result.success(Map.of("original", text, "filtered", filtered));
    }
}
