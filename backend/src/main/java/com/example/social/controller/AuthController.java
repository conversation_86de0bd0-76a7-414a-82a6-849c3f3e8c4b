package com.example.social.controller;

import com.example.social.common.Result;
import com.example.social.dto.LoginRequest;
import com.example.social.dto.RegisterRequest;
import com.example.social.dto.UserDTO;
import com.example.social.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final UserService userService;

    @PostMapping("/register")
    public Result<UserDTO> register(@Validated @RequestBody RegisterRequest request) {
        UserDTO userDTO = userService.register(request);
        return Result.success(userDTO);
    }

    @PostMapping("/login")
    public Result<Map<String, Object>> login(@Validated @RequestBody LoginRequest request) {
        String token = userService.login(request);
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        return Result.success(result);
    }

    @GetMapping("/info")
    public Result<UserDTO> getUserInfo() {
        UserDTO userDTO = userService.getCurrentUser();
        return Result.success(userDTO);
    }
} 