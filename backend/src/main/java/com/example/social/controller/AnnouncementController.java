package com.example.social.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.example.social.common.Result;
import com.example.social.dto.AnnouncementDTO;
import com.example.social.dto.AnnouncementCommentDTO;
import com.example.social.service.AnnouncementService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 公告控制器
 */
@RestController
@RequestMapping("/announcement")
@RequiredArgsConstructor
public class AnnouncementController {
    
    private final AnnouncementService announcementService;
    
    /**
     * 分页查询公告列表
     */
    @GetMapping("/page")
    public Result<Page<AnnouncementDTO>> page(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String keyword) {
        return Result.success(announcementService.page(pageNum, pageSize, category, keyword));
    }
    
    /**
     * 管理员分页查询公告列表
     */
    @GetMapping("/admin/page")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Page<AnnouncementDTO>> adminPage(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword) {
        return Result.success(announcementService.adminPage(pageNum, pageSize, category, status, keyword));
    }
    
    /**
     * 根据ID获取公告详情
     */
    @GetMapping("/{id}")
    public Result<AnnouncementDTO> getById(@PathVariable Long id) {
        return Result.success(announcementService.getById(id));
    }
    
    /**
     * 创建公告（管理员）
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<AnnouncementDTO> create(@Validated @RequestBody AnnouncementDTO dto) {
        return Result.success(announcementService.create(dto));
    }
    
    /**
     * 更新公告（管理员）
     */
    @PutMapping
    @PreAuthorize("hasRole('ADMIN')")
    public Result<AnnouncementDTO> update(@Validated @RequestBody AnnouncementDTO dto) {
        return Result.success(announcementService.update(dto));
    }
    
    /**
     * 删除公告（管理员）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> delete(@PathVariable Long id) {
        announcementService.delete(id);
        return Result.success();
    }
    
    /**
     * 点赞/取消点赞公告
     */
    @PostMapping("/{id}/like")
    public Result<Void> toggleLike(@PathVariable Long id) {
        announcementService.toggleLike(id);
        return Result.success();
    }
    
    /**
     * 添加评论
     */
    @PostMapping("/{id}/comment")
    public Result<AnnouncementCommentDTO> addComment(
            @PathVariable Long id,
            @RequestBody Map<String, Object> params) {
        String content = (String) params.get("content");
        Long parentId = params.get("parentId") != null ? 
                Long.valueOf(params.get("parentId").toString()) : null;
        return Result.success(announcementService.addComment(id, content, parentId));
    }
    
    /**
     * 删除评论
     */
    @DeleteMapping("/comment/{id}")
    public Result<Void> deleteComment(@PathVariable Long id) {
        announcementService.deleteComment(id);
        return Result.success();
    }
    
    /**
     * 管理员删除评论
     */
    @DeleteMapping("/admin/comment/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> adminDeleteComment(@PathVariable Long id) {
        announcementService.adminDeleteComment(id);
        return Result.success();
    }
    
    /**
     * 点赞/取消点赞评论
     */
    @PostMapping("/comment/{id}/like")
    public Result<Void> toggleCommentLike(@PathVariable Long id) {
        announcementService.toggleCommentLike(id);
        return Result.success();
    }
    
    /**
     * 获取公告评论列表
     */
    @GetMapping("/{id}/comments")
    public Result<Page<AnnouncementCommentDTO>> getComments(
            @PathVariable Long id,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize) {
        return Result.success(announcementService.getComments(id, pageNum, pageSize));
    }
}
