-- MySQL不支持创建枚举类型，我们使用VARCHAR来存储隐私级别
-- 创建用户隐私设置表
CREATE TABLE IF NOT EXISTS `user_privacy_settings` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `profile_visibility` varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT '个人资料可见性: PUBLIC-公开, FRIENDS_ONLY-仅好友可见, PRIVATE-私密',
  `real_name_visibility` varchar(20) NOT NULL DEFAULT 'FRIENDS_ONLY' COMMENT '真实姓名可见性',
  `phone_visibility` varchar(20) NOT NULL DEFAULT 'PRIVATE' COMMENT '手机号可见性',
  `address_visibility` varchar(20) NOT NULL DEFAULT 'PRIVATE' COMMENT '地址可见性',
  `posts_visibility` varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT '动态可见性',
  `friends_list_visibility` varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT '好友列表可见性',
  `online_status_visibility` varchar(20) NOT NULL DEFAULT 'PUBLIC' COMMENT '在线状态可见性',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户隐私设置表';

-- 初始化隐私设置的SQL已移至init_privacy_settings.sql文件
-- 请先创建表，再执行初始化脚本
