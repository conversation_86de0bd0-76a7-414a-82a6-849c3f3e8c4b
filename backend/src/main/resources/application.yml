server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: social-network
  main:
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ****************************************************************************************
    username: root
    password: NimbleX@123
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  servlet:

    multipart:
      max-file-size: 10MB 
      max-request-size: 10MB

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.example.social.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

jwt:
  secret: your-secret-key-least-32-characters
  expiration: 86400000  # 24小时

file:
  upload:
    path: D:\k-anonymity\backend\src\main\resources\files  # 文件上传路径